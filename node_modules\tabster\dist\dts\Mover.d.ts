/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { DummyInputManager, TabsterPart, WeakHTMLElement } from "./Utils";
declare class MoverDummyManager extends DummyInputManager {
    private _tabster;
    private _getMemorized;
    constructor(element: WeakHTMLElement, tabster: Types.TabsterCore, getMemorized: () => WeakHTMLElement | undefined, sys: Types.SysProps | undefined);
    private _onFocusDummyInput;
}
export declare class Mover extends TabsterPart<Types.MoverProps> implements Types.Mover {
    private _unobserve;
    private _intersectionObserver;
    private _setCurrentTimer;
    private _current;
    private _prevCurrent;
    private _visible;
    private _fullyVisible;
    private _win;
    private _onDispose;
    private _allElements;
    private _updateQueue;
    private _updateTimer;
    visibilityTolerance: number;
    dummyManager: MoverDummyManager | undefined;
    constructor(tabster: Types.TabsterCore, element: HTMLElement, onDispose: (mover: Mover) => void, props: Types.MoverProps, sys: Types.SysProps | undefined);
    dispose(): void;
    setCurrent(element: HTMLElement | undefined): void;
    getCurrent(): HTMLElement | null;
    findNextTabbable(currentElement?: HTMLElement, referenceElement?: HTMLElement, isBackward?: boolean, ignoreAccessibility?: boolean): Types.NextTabbable | null;
    acceptElement(element: HTMLElement, state: Types.FocusableAcceptElementState): number | undefined;
    private _onIntersection;
    private _observeState;
    getState(element: HTMLElement): Types.MoverElementState | undefined;
}
export declare class MoverAPI implements Types.MoverAPI {
    private _tabster;
    private _win;
    private _movers;
    private _ignoredInputTimer;
    private _ignoredInputResolve;
    constructor(tabster: Types.TabsterCore, getWindow: Types.GetWindow);
    private _init;
    dispose(): void;
    createMover(element: HTMLElement, props: Types.MoverProps, sys: Types.SysProps | undefined): Types.Mover;
    private _onMoverDispose;
    private _onFocus;
    moveFocus(fromElement: HTMLElement, key: Types.MoverKey): HTMLElement | null;
    private _moveFocus;
    private _onKeyDown;
    private _onMoveFocus;
    private _onMemorizedElement;
    private _isIgnoredInput;
}
export {};
