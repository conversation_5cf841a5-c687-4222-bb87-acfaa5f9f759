{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export { createOverflowManager } from './overflowManager';\nexport type {\n  ObserveOptions,\n  OnUpdateItemVisibility,\n  OnUpdateItemVisibilityPayload,\n  OnUpdateOverflow,\n  OverflowAxis,\n  OverflowDirection,\n  OverflowEventPayload,\n  OverflowGroupState,\n  OverflowItemEntry,\n  OverflowDividerEntry,\n  OverflowManager,\n} from './types';\n"], "names": ["createOverflowManager"], "rangeMappings": ";;;;;;;;;;", "mappings": ";;;;+BAASA;;;eAAAA,sCAAqB;;;iCAAQ"}