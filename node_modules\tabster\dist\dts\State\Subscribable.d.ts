/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "../Types";
export declare abstract class Subscribable<A, B = undefined> implements Types.Subscribable<A, B> {
    protected _val: A | undefined;
    private _callbacks;
    dispose(): void;
    subscribe(callback: Types.SubscribableCallback<A, B>): void;
    subscribeFirst(callback: Types.SubscribableCallback<A, B>): void;
    unsubscribe(callback: Types.SubscribableCallback<A, B>): void;
    protected setVal(val: A, detail: B): void;
    protected getVal(): A | undefined;
    protected trigger(val: A, detail: B): void;
    private _callCallbacks;
}
