{"version": 3, "file": "auxiliaryAuthenticationHeaderPolicy.d.ts", "sourceRoot": "", "sources": ["../../../src/policies/auxiliaryAuthenticationHeaderPolicy.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAmB,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACzE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAEjD,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAKrD;;GAEG;AACH,eAAO,MAAM,uCAAuC,wCAAwC,CAAC;AAG7F;;GAEG;AACH,MAAM,WAAW,0CAA0C;IACzD;;;OAGG;IACH,WAAW,CAAC,EAAE,eAAe,EAAE,CAAC;IAChC;;OAEG;IACH,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IAC1B;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAYD;;;;;GAKG;AACH,wBAAgB,mCAAmC,CACjD,OAAO,EAAE,0CAA0C,GAClD,cAAc,CAmDhB"}