/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
export interface TabsterMoveFocusEventDetail {
    by: "mover" | "groupper" | "modalizer" | "root" | "deloser";
    owner: HTMLElement;
    next: HTMLElement | null;
    relatedEvent?: KeyboardEvent;
}
export interface MoverMoveFocusEventDetail {
    key: Types.MoverKey;
}
export interface MoverMemorizedElementEventDetail {
    memorizedElement: HTMLElement | undefined;
}
export interface GroupperMoveFocusEventDetail {
    action: Types.GroupperMoveFocusAction;
}
export interface ModalizerEventDetail {
    id: string;
    element: HTMLElement;
}
export interface RootFocusEventDetail {
    element: HTMLElement;
}
