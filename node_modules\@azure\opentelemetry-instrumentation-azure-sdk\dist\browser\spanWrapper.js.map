{"version": 3, "file": "spanWrapper.js", "sourceRoot": "", "sources": ["../../src/spanWrapper.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAEpD,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,MAAM,OAAO,wBAAwB;IAGnC,YAAY,IAAU;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,SAAS,CAAC,MAAkB;QAC1B,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,IAAI,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACjE,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACvF,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,YAAY,CAAC,IAAY,EAAE,KAAc;QACvC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,GAAG;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,eAAe,CAAC,SAAyB;QACvC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,UAA2B,EAAE;QAClD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IACvF,CAAC;IAED;;;;;OAKG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF;AAED;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAC,KAAiC;IAC1D,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,YAAY,IAAI,KAAK,EAAE,CAAC;QACzE,OAAO,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;IAClC,CAAC;IAED,gGAAgG;IAChG,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Span } from \"@opentelemetry/api\";\nimport { SpanStatusCode } from \"@opentelemetry/api\";\nimport type { SpanStatus, TracingSpan, AddEventOptions } from \"@azure/core-tracing\";\nimport { isAttributeValue, sanitizeAttributes } from \"@opentelemetry/core\";\nimport { logger } from \"./logger.js\";\n\nexport class OpenTelemetrySpanWrapper implements TracingSpan {\n  private _span: Span;\n\n  constructor(span: Span) {\n    this._span = span;\n  }\n\n  setStatus(status: SpanStatus): void {\n    if (status.status === \"error\" && isRecordableError(status.error)) {\n      if (status.error) {\n        this._span.setStatus({ code: SpanStatusCode.ERROR, message: status.error.toString() });\n        this.recordException(status.error);\n      } else {\n        this._span.setStatus({ code: SpanStatusCode.ERROR });\n      }\n    } else if (status.status === \"success\") {\n      logger.verbose(\"Leaving span with status UNSET per OpenTelemetry spec.\");\n    }\n  }\n\n  setAttribute(name: string, value: unknown): void {\n    if (value !== null && value !== undefined && isAttributeValue(value)) {\n      this._span.setAttribute(name, value);\n    }\n  }\n\n  end(): void {\n    this._span.end();\n  }\n\n  recordException(exception: string | Error): void {\n    this._span.recordException(exception);\n  }\n\n  isRecording(): boolean {\n    return this._span.isRecording();\n  }\n\n  addEvent(name: string, options: AddEventOptions = {}): void {\n    this._span.addEvent(name, sanitizeAttributes(options.attributes), options.startTime);\n  }\n\n  /**\n   * Allows getting the wrapped span as needed.\n   * @internal\n   *\n   * @returns The underlying span\n   */\n  unwrap(): Span {\n    return this._span;\n  }\n}\n\n/**\n * Determines if an error should be recorded on the span.\n *\n * By default, all errors will mark the span status as error\n * except for {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/304} which is expected\n * when the cached resource is still valid in a conditional request.\n */\nfunction isRecordableError(error: string | Error | undefined): boolean {\n  if (error !== null && typeof error === \"object\" && \"statusCode\" in error) {\n    return error.statusCode !== 304;\n  }\n\n  // we do not have enough information to determine if this error is recordable so we assume it is\n  return true;\n}\n"]}