/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { DummyInputManager, TabsterPart, WeakHTMLElement } from "./Utils";
/**
 * Manages the dummy inputs for the Modalizer.
 */
declare class ModalizerDummyManager extends DummyInputManager {
    constructor(element: WeakHTMLElement, tabster: Types.TabsterCore, sys: Types.SysProps | undefined);
}
export declare class Modalizer extends TabsterPart<Types.ModalizerProps> implements Types.Modalizer {
    userId: string;
    private _isActive;
    private _wasFocused;
    private _onDispose;
    private _activeElements;
    dummyManager: ModalizerDummyManager | undefined;
    constructor(tabster: Types.TabsterCore, element: HTMLElement, onDispose: (modalizer: Modalizer) => void, props: Types.ModalizerProps, sys: Types.SysProps | undefined, activeElements: WeakHTMLElement<HTMLElement>[]);
    makeActive(isActive: boolean): void;
    focused(noIncrement?: boolean): number;
    setProps(props: Types.ModalizerProps): void;
    dispose(): void;
    isActive(): boolean;
    contains(element: HTMLElement): boolean;
    findNextTabbable(currentElement?: HTMLElement, referenceElement?: HTMLElement, isBackward?: boolean, ignoreAccessibility?: boolean): Types.NextTabbable | null;
    private _dispatchEvent;
    private _remove;
}
export declare class ModalizerAPI implements Types.ModalizerAPI {
    private _tabster;
    private _win;
    private _restoreModalizerFocusTimer;
    private _modalizers;
    private _parts;
    private _augMap;
    private _aug;
    private _hiddenUpdateTimer;
    private _alwaysAccessibleSelector;
    private _accessibleCheck;
    private _activationHistory;
    activeId: string | undefined;
    currentIsOthersAccessible: boolean | undefined;
    activeElements: WeakHTMLElement<HTMLElement>[];
    constructor(tabster: Types.TabsterCore, alwaysAccessibleSelector?: string, accessibleCheck?: Types.ModalizerElementAccessibleCheck);
    dispose(): void;
    createModalizer(element: HTMLElement, props: Types.ModalizerProps, sys: Types.SysProps | undefined): Types.Modalizer;
    private _onModalizerDispose;
    private _onKeyDown;
    isAugmented(element: HTMLElement): boolean;
    hiddenUpdate(): void;
    setActive(modalizer: Types.Modalizer | undefined): void;
    focus(elementFromModalizer: HTMLElement, noFocusFirst?: boolean, noFocusDefault?: boolean): boolean;
    activate(modalizerElementOrContainer: HTMLElement | undefined): boolean;
    acceptElement(element: HTMLElement, state: Types.FocusableAcceptElementState): number | undefined;
    private _hiddenUpdate;
    /**
     * Subscribes to the focus state and handles modalizer related focus events
     * @param focusedElement - Element that is focused
     * @param detail - Additional data about the focus event
     */
    private _onFocus;
    /**
     * Called when an element is focused outside of an active modalizer.
     * Attempts to pull focus back into the active modalizer
     * @param outsideElement - An element being focused outside of the modalizer
     */
    private _restoreModalizerFocus;
}
export {};
