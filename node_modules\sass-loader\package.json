{"name": "sass-loader", "version": "13.3.3", "description": "Sass loader for webpack", "license": "MIT", "repository": "webpack-contrib/sass-loader", "author": "<PERSON><PERSON>", "homepage": "https://github.com/webpack-contrib/sass-loader", "bugs": "https://github.com/webpack-contrib/sass-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "lint:spelling": "cspell \"**/*.*\"", "test:watch": "npm run test:only -- --watch", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "fibers": {"optional": true}}, "dependencies": {"neo-async": "^2.6.2"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.6.2", "bootstrap-sass": "^3.4.1", "bootstrap-v4": "npm:bootstrap@^4.5.3", "bootstrap-v5": "npm:bootstrap@^5.0.1", "cross-env": "^7.0.3", "cspell": "^6.31.2", "css-loader": "^6.8.1", "del": "^6.1.1", "del-cli": "^4.0.1", "enhanced-resolve": "^5.15.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.0", "file-loader": "^6.2.0", "foundation-sites": "^6.7.5", "husky": "^8.0.3", "jest": "^29.6.2", "jest-environment-node-single-context": "^29.1.0", "lint-staged": "^13.2.3", "material-components-web": "^9.0.0", "memfs": "^3.5.1", "node-sass": "^8.0.0", "node-sass-glob-importer": "^5.3.2", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "sass": "^1.64.2", "sass-embedded": "^1.64.2", "semver": "^7.5.4", "standard-version": "^9.3.1", "style-loader": "^3.3.3", "webpack": "^5.88.2"}, "keywords": ["sass", "libsass", "webpack", "loader"]}