{"version": "1.5.0-beta", "compilerOptions": {"target": "es5", "module": "commonjs", "isolatedModules": false, "jsx": "react", "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": false, "noImplicitAny": true, "removeComments": true, "noLib": false, "preserveConstEnums": true, "suppressImplicitAnyIndexErrors": true}, "compileOnSave": false, "filesGlob": ["./**/*.ts", "!./core/es*.ts", "!./**/*.d.ts"], "files": ["core/abstractobserver.ts", "core/anonymousobservable.ts", "core/anonymousobserver.ts", "core/backpressure/controlled.ts", "core/backpressure/pausable.ts", "core/backpressure/pausablebuffered.ts", "core/backpressure/pauser.ts", "core/backpressure/stopandwait.ts", "core/backpressure/windowed.ts", "core/checkedobserver.ts", "core/concurrency/currentthreadscheduler.ts", "core/concurrency/defaultscheduler.ts", "core/concurrency/historicalscheduler.ts", "core/concurrency/immediatescheduler.ts", "core/concurrency/scheduleditem.ts", "core/concurrency/scheduleperiodicrecursive.ts", "core/concurrency/scheduler.periodic.ts", "core/concurrency/scheduler.recursive.ts", "core/concurrency/scheduler.ts", "core/concurrency/scheduler.wrappers.ts", "core/concurrency/virtualtimescheduler.ts", "core/disposables/booleandisposable.ts", "core/disposables/compositedisposable.ts", "core/disposables/disposable.ts", "core/disposables/refcountdisposable.ts", "core/internal/bindcallback.ts", "core/internal/errors.ts", "core/internal/isequal.ts", "core/internal/priorityqueue.ts", "core/internal/util.ts", "core/joins/pattern.ts", "core/joins/plan.ts", "core/linq/connectableobservable.ts", "core/linq/groupedobservable.ts", "core/linq/observable/amb.ts", "core/linq/observable/ambproto.ts", "core/linq/observable/and.ts", "core/linq/observable/asobservable.ts", "core/linq/observable/average.ts", "core/linq/observable/buffer.ts", "core/linq/observable/bufferwithcount.ts", "core/linq/observable/bufferwithtime.ts", "core/linq/observable/bufferwithtimeorcount.ts", "core/linq/observable/case.ts", "core/linq/observable/catch.ts", "core/linq/observable/catchproto.ts", "core/linq/observable/combinelatest.ts", "core/linq/observable/combinelatestproto.ts", "core/linq/observable/concat.ts", "core/linq/observable/concatall.ts", "core/linq/observable/concatmap.ts", "core/linq/observable/concatmapobserver.ts", "core/linq/observable/concatproto.ts", "core/linq/observable/count.ts", "core/linq/observable/create.ts", "core/linq/observable/debounce.ts", "core/linq/observable/defaultifempty.ts", "core/linq/observable/defer.ts", "core/linq/observable/delay.ts", "core/linq/observable/delaysubscription.ts", "core/linq/observable/dematerialize.ts", "core/linq/observable/distinct.ts", "core/linq/observable/distinctuntilchanged.ts", "core/linq/observable/dowhile.ts", "core/linq/observable/elementat.ts", "core/linq/observable/empty.ts", "core/linq/observable/every.ts", "core/linq/observable/expand.ts", "core/linq/observable/filter.ts", "core/linq/observable/finally.ts", "core/linq/observable/find.ts", "core/linq/observable/findindex.ts", "core/linq/observable/first.ts", "core/linq/observable/flatmap.ts", "core/linq/observable/flatmapfirst.ts", "core/linq/observable/flatmaplatest.ts", "core/linq/observable/flatmapwithmaxconcurrent.ts", "core/linq/observable/for.ts", "core/linq/observable/forkjoin.ts", "core/linq/observable/forkjoinproto.ts", "core/linq/observable/from.ts", "core/linq/observable/fromarray.ts", "core/linq/observable/fromcallback.ts", "core/linq/observable/fromevent.ts", "core/linq/observable/fromeventpattern.ts", "core/linq/observable/fromnodecallback.ts", "core/linq/observable/frompromise.ts", "core/linq/observable/generate.ts", "core/linq/observable/generatewithabsolutetime.ts", "core/linq/observable/generatewithrelativetime.ts", "core/linq/observable/groupby.ts", "core/linq/observable/groupbyuntil.ts", "core/linq/observable/groupjoin.ts", "core/linq/observable/if.ts", "core/linq/observable/ignoreelements.ts", "core/linq/observable/includes.ts", "core/linq/observable/indexof.ts", "core/linq/observable/interval.ts", "core/linq/observable/isempty.ts", "core/linq/observable/join.ts", "core/linq/observable/jortsort.ts", "core/linq/observable/jortsortuntil.ts", "core/linq/observable/just.ts", "core/linq/observable/last.ts", "core/linq/observable/let.ts", "core/linq/observable/manyselect.ts", "core/linq/observable/map.ts", "core/linq/observable/materialize.ts", "core/linq/observable/max.ts", "core/linq/observable/maxby.ts", "core/linq/observable/merge.ts", "core/linq/observable/mergeall.ts", "core/linq/observable/mergedelayerror.ts", "core/linq/observable/mergeproto.ts", "core/linq/observable/min.ts", "core/linq/observable/minby.ts", "core/linq/observable/multicast.ts", "core/linq/observable/never.ts", "core/linq/observable/observeon.ts", "core/linq/observable/of.ts", "core/linq/observable/ofarraychanges.ts", "core/linq/observable/ofobjectchanges.ts", "core/linq/observable/onerrorresumenext.ts", "core/linq/observable/onerrorresumenextproto.ts", "core/linq/observable/pairs.ts", "core/linq/observable/pairwise.ts", "core/linq/observable/partition.ts", "core/linq/observable/pipe.ts", "core/linq/observable/pluck.ts", "core/linq/observable/publish.ts", "core/linq/observable/publishlast.ts", "core/linq/observable/publishvalue.ts", "core/linq/observable/range.ts", "core/linq/observable/reduce.ts", "core/linq/observable/repeat.ts", "core/linq/observable/repeatproto.ts", "core/linq/observable/replay.ts", "core/linq/observable/retry.ts", "core/linq/observable/retrywhen.ts", "core/linq/observable/sample.ts", "core/linq/observable/scan.ts", "core/linq/observable/selectmanyobserver.ts", "core/linq/observable/sequenceequal.ts", "core/linq/observable/share.ts", "core/linq/observable/sharereplay.ts", "core/linq/observable/sharevalue.ts", "core/linq/observable/single.ts", "core/linq/observable/singleinstance.ts", "core/linq/observable/skip.ts", "core/linq/observable/skiplast.ts", "core/linq/observable/skiplastwithtime.ts", "core/linq/observable/skipuntil.ts", "core/linq/observable/skipuntilwithtime.ts", "core/linq/observable/skipwhile.ts", "core/linq/observable/skipwithtime.ts", "core/linq/observable/some.ts", "core/linq/observable/spawn.ts", "core/linq/observable/start.ts", "core/linq/observable/startasync.ts", "core/linq/observable/startwith.ts", "core/linq/observable/subscribeon.ts", "core/linq/observable/sum.ts", "core/linq/observable/switch.ts", "core/linq/observable/switchfirst.ts", "core/linq/observable/take.ts", "core/linq/observable/takelast.ts", "core/linq/observable/takelastbuffer.ts", "core/linq/observable/takelastbufferwithtime.ts", "core/linq/observable/takelastwithtime.ts", "core/linq/observable/takeuntil.ts", "core/linq/observable/takeuntilwithtime.ts", "core/linq/observable/takewhile.ts", "core/linq/observable/takewithtime.ts", "core/linq/observable/tap.ts", "core/linq/observable/thendo.ts", "core/linq/observable/throttle.ts", "core/linq/observable/throw.ts", "core/linq/observable/timeinterval.ts", "core/linq/observable/timeout.ts", "core/linq/observable/timer.ts", "core/linq/observable/timestamp.ts", "core/linq/observable/toarray.ts", "core/linq/observable/toasync.ts", "core/linq/observable/tomap.ts", "core/linq/observable/topromise.ts", "core/linq/observable/toset.ts", "core/linq/observable/transduce.ts", "core/linq/observable/using.ts", "core/linq/observable/when.ts", "core/linq/observable/while.ts", "core/linq/observable/window.ts", "core/linq/observable/windowwithcount.ts", "core/linq/observable/windowwithtime.ts", "core/linq/observable/windowwithtimeorcount.ts", "core/linq/observable/withlatestfrom.ts", "core/linq/observable/zip.ts", "core/linq/observable/zipiterable.ts", "core/linq/observable/zipproto.ts", "core/notification.ts", "core/observable.ts", "core/observer-extras.ts", "core/observer-lite.ts", "core/observer.ts", "core/scheduledobserver.ts", "core/subjects/anonymoussubject.ts", "core/subjects/asyncsubject.ts", "core/subjects/behaviorsubject.ts", "core/subjects/replaysubject.ts", "core/subjects/subject.ts", "core/testing/mockdisposable.ts", "core/testing/mockobserver.ts", "core/testing/reactivetest.ts", "core/testing/recorded.ts", "core/testing/subscription.ts", "core/testing/testscheduler.ts"], "exclude": []}