// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
/*
 * NOTE: When moving this file, please update "browser" section in package.json
 */
export const decompressResponsePolicyName = "decompressResponsePolicy";
/**
 * decompressResponsePolicy is not supported in the browser and attempting
 * to use it will raise an error.
 */
export function decompressResponsePolicy() {
    throw new Error("decompressResponsePolicy is not supported in browser environment");
}
//# sourceMappingURL=decompressResponsePolicy-browser.mjs.map