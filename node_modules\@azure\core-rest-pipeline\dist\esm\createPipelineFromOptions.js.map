{"version": 3, "file": "createPipelineFromOptions.js", "sourceRoot": "", "sources": ["../../src/createPipelineFromOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAyB,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAC3E,OAAO,EAAiB,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAEnE,OAAO,EAA8B,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC1F,OAAO,EAA+B,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAC7F,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACrF,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAyD5D;;;GAGG;AACH,MAAM,UAAU,yBAAyB,CAAC,OAAgC;;IACxE,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC;IAEvC,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACtD,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IAChF,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC9D,QAAQ,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAA,OAAO,CAAC,gBAAgB,0CAAE,yBAAyB,CAAC,CAAC,CAAC;IAClG,sEAAsE;IACtE,gEAAgE;IAChE,qEAAqE;IACrE,QAAQ,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;IACrE,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IACjF,QAAQ,CAAC,SAAS,CAAC,aAAa,iCAAM,OAAO,CAAC,gBAAgB,GAAK,OAAO,CAAC,cAAc,EAAG,EAAE;QAC5F,UAAU,EAAE,OAAO;KACpB,CAAC,CAAC;IACH,IAAI,UAAU,EAAE,CAAC;QACf,+DAA+D;QAC/D,kDAAkD;QAClD,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;IACvF,CAAC;IACD,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;IAE9E,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { type LogPolicyOptions, logPolicy } from \"./policies/logPolicy.js\";\nimport { type Pipeline, createEmptyPipeline } from \"./pipeline.js\";\nimport type { PipelineRetryOptions, TlsSettings, ProxySettings } from \"./interfaces.js\";\nimport { type RedirectPolicyOptions, redirectPolicy } from \"./policies/redirectPolicy.js\";\nimport { type UserAgentPolicyOptions, userAgentPolicy } from \"./policies/userAgentPolicy.js\";\nimport { multipartPolicy, multipartPolicyName } from \"./policies/multipartPolicy.js\";\nimport { decompressResponsePolicy } from \"./policies/decompressResponsePolicy.js\";\nimport { defaultRetryPolicy } from \"./policies/defaultRetryPolicy.js\";\nimport { formDataPolicy } from \"./policies/formDataPolicy.js\";\nimport { isNodeLike } from \"@azure/core-util\";\nimport { proxyPolicy } from \"./policies/proxyPolicy.js\";\nimport { setClientRequestIdPolicy } from \"./policies/setClientRequestIdPolicy.js\";\nimport { tlsPolicy } from \"./policies/tlsPolicy.js\";\nimport { tracingPolicy } from \"./policies/tracingPolicy.js\";\n\n/**\n * Defines options that are used to configure the HTTP pipeline for\n * an SDK client.\n */\nexport interface PipelineOptions {\n  /**\n   * Options that control how to retry failed requests.\n   */\n  retryOptions?: PipelineRetryOptions;\n\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxySettings;\n\n  /** Options for configuring TLS authentication */\n  tlsOptions?: TlsSettings;\n\n  /**\n   * Options for how redirect responses are handled.\n   */\n  redirectOptions?: RedirectPolicyOptions;\n\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentPolicyOptions;\n\n  /**\n   * Options for setting common telemetry and tracing info to outgoing requests.\n   */\n  telemetryOptions?: TelemetryOptions;\n}\n\n/**\n * Defines options that are used to configure common telemetry and tracing info\n */\nexport interface TelemetryOptions {\n  /**\n   * The name of the header to pass the request ID to.\n   */\n  clientRequestIdHeaderName?: string;\n}\n\n/**\n * Defines options that are used to configure internal options of\n * the HTTP pipeline for an SDK client.\n */\nexport interface InternalPipelineOptions extends PipelineOptions {\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n}\n\n/**\n * Create a new pipeline with a default set of customizable policies.\n * @param options - Options to configure a custom pipeline.\n */\nexport function createPipelineFromOptions(options: InternalPipelineOptions): Pipeline {\n  const pipeline = createEmptyPipeline();\n\n  if (isNodeLike) {\n    if (options.tlsOptions) {\n      pipeline.addPolicy(tlsPolicy(options.tlsOptions));\n    }\n    pipeline.addPolicy(proxyPolicy(options.proxyOptions));\n    pipeline.addPolicy(decompressResponsePolicy());\n  }\n\n  pipeline.addPolicy(formDataPolicy(), { beforePolicies: [multipartPolicyName] });\n  pipeline.addPolicy(userAgentPolicy(options.userAgentOptions));\n  pipeline.addPolicy(setClientRequestIdPolicy(options.telemetryOptions?.clientRequestIdHeaderName));\n  // The multipart policy is added after policies with no phase, so that\n  // policies can be added between it and formDataPolicy to modify\n  // properties (e.g., making the boundary constant in recorded tests).\n  pipeline.addPolicy(multipartPolicy(), { afterPhase: \"Deserialize\" });\n  pipeline.addPolicy(defaultRetryPolicy(options.retryOptions), { phase: \"Retry\" });\n  pipeline.addPolicy(tracingPolicy({ ...options.userAgentOptions, ...options.loggingOptions }), {\n    afterPhase: \"Retry\",\n  });\n  if (isNodeLike) {\n    // Both XHR and Fetch expect to handle redirects automatically,\n    // so only include this policy when we're in Node.\n    pipeline.addPolicy(redirectPolicy(options.redirectOptions), { afterPhase: \"Retry\" });\n  }\n  pipeline.addPolicy(logPolicy(options.loggingOptions), { afterPhase: \"Sign\" });\n\n  return pipeline;\n}\n"]}