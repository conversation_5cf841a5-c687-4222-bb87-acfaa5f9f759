import type { Instrumenter, TracingSpan } from "./interfaces.js";
export declare function createDefaultTracingSpan(): TracingSpan;
export declare function createDefaultInstrumenter(): Instrumenter;
/**
 * Extends the Azure SDK with support for a given instrumenter implementation.
 *
 * @param instrumenter - The instrumenter implementation to use.
 */
export declare function useInstrumenter(instrumenter: Instrumenter): void;
/**
 * Gets the currently set instrumenter, a No-Op instrumenter by default.
 *
 * @returns The currently set instrumenter
 */
export declare function getInstrumenter(): Instrumenter;
//# sourceMappingURL=instrumenter.d.ts.map