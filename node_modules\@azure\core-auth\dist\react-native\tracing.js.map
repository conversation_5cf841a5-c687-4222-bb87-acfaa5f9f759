{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../src/tracing.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// The interfaces in this file should be kept in sync with those\n// found in the `@azure/core-tracing` package.\n\n/**\n * An interface structurally compatible with OpenTelemetry.\n */\nexport interface TracingContext {\n  /**\n   * Get a value from the context.\n   *\n   * @param key - key which identifies a context value\n   */\n  getValue(key: symbol): unknown;\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key - context key for which to set the value\n   * @param value - value to set for the given key\n   */\n  setValue(key: symbol, value: unknown): TracingContext;\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key - context key for which to clear a value\n   */\n  deleteValue(key: symbol): TracingContext;\n}\n"]}