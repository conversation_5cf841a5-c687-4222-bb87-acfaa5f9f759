{"version": 3, "file": "exponentialRetryStrategy.js", "sourceRoot": "", "sources": ["../../../src/retryStrategies/exponentialRetryStrategy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,yBAAyB,EAAE,MAAM,kBAAkB,CAAC;AAE7D,OAAO,EAAE,yBAAyB,EAAE,MAAM,8BAA8B,CAAC;AAEzE,gCAAgC;AAChC,MAAM,6BAA6B,GAAG,IAAI,CAAC;AAC3C,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpD;;;;GAIG;AACH,MAAM,UAAU,wBAAwB,CACtC,UAuBI,EAAE;;IAEN,MAAM,aAAa,GAAG,MAAA,OAAO,CAAC,cAAc,mCAAI,6BAA6B,CAAC;IAC9E,MAAM,gBAAgB,GAAG,MAAA,OAAO,CAAC,iBAAiB,mCAAI,iCAAiC,CAAC;IAExF,IAAI,cAAc,GAAG,aAAa,CAAC;IAEnC,OAAO;QACL,IAAI,EAAE,0BAA0B;QAChC,KAAK,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE;YAC3C,MAAM,kBAAkB,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,kBAAkB,GAAG,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC;YAE5E,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,yBAAyB,GAAG,aAAa,IAAI,OAAO,CAAC,qBAAqB,CAAC;YACjF,MAAM,eAAe,GAAG,QAAQ,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5F,IAAI,eAAe,IAAI,yBAAyB,IAAI,kBAAkB,EAAE,CAAC;gBACvE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YAChC,CAAC;YAED,IAAI,aAAa,IAAI,CAAC,kBAAkB,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC3D,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;YACzC,CAAC;YAED,6CAA6C;YAC7C,MAAM,gBAAgB,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAClE,yCAAyC;YACzC,MAAM,uBAAuB,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YAC7E,gFAAgF;YAChF,mEAAmE;YACnE,cAAc;gBACZ,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,CAAC,CAAC,EAAE,uBAAuB,GAAG,CAAC,CAAC,CAAC;YAC1F,OAAO,EAAE,cAAc,EAAE,CAAC;QAC5B,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,0BAA0B,CAAC,QAA2B;IACpE,OAAO,OAAO,CACZ,QAAQ;QACN,QAAQ,CAAC,MAAM,KAAK,SAAS;QAC7B,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACnD,QAAQ,CAAC,MAAM,KAAK,GAAG;QACvB,QAAQ,CAAC,MAAM,KAAK,GAAG,CAC1B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,GAAe;IAC3C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,CACL,GAAG,CAAC,IAAI,KAAK,WAAW;QACxB,GAAG,CAAC,IAAI,KAAK,iBAAiB;QAC9B,GAAG,CAAC,IAAI,KAAK,cAAc;QAC3B,GAAG,CAAC,IAAI,KAAK,YAAY;QACzB,GAAG,CAAC,IAAI,KAAK,QAAQ;QACrB,GAAG,CAAC,IAAI,KAAK,WAAW,CACzB,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport type { RestError } from \"../restError.js\";\nimport { getRandomIntegerInclusive } from \"@azure/core-util\";\nimport type { RetryStrategy } from \"./retryStrategy.js\";\nimport { isThrottlingRetryResponse } from \"./throttlingRetryStrategy.js\";\n\n// intervals are in milliseconds\nconst DEFAULT_CLIENT_RETRY_INTERVAL = 1000;\nconst DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;\n\n/**\n * A retry strategy that retries with an exponentially increasing delay in these two cases:\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails (408, greater or equal than 500, except for 501 and 505).\n */\nexport function exponentialRetryStrategy(\n  options: {\n    /**\n     * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n     * (1 second.) The delay increases exponentially with each retry up to a maximum\n     * specified by maxRetryDelayInMs.\n     */\n    retryDelayInMs?: number;\n\n    /**\n     * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n     * to 64000 (64 seconds).\n     */\n    maxRetryDelayInMs?: number;\n\n    /**\n     * If true it won't retry if it received a system error.\n     */\n    ignoreSystemErrors?: boolean;\n\n    /**\n     * If true it won't retry if it received a non-fatal HTTP status code.\n     */\n    ignoreHttpStatusCodes?: boolean;\n  } = {},\n): RetryStrategy {\n  const retryInterval = options.retryDelayInMs ?? DEFAULT_CLIENT_RETRY_INTERVAL;\n  const maxRetryInterval = options.maxRetryDelayInMs ?? DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n\n  let retryAfterInMs = retryInterval;\n\n  return {\n    name: \"exponentialRetryStrategy\",\n    retry({ retryCount, response, responseError }) {\n      const matchedSystemError = isSystemError(responseError);\n      const ignoreSystemErrors = matchedSystemError && options.ignoreSystemErrors;\n\n      const isExponential = isExponentialRetryResponse(response);\n      const ignoreExponentialResponse = isExponential && options.ignoreHttpStatusCodes;\n      const unknownResponse = response && (isThrottlingRetryResponse(response) || !isExponential);\n\n      if (unknownResponse || ignoreExponentialResponse || ignoreSystemErrors) {\n        return { skipStrategy: true };\n      }\n\n      if (responseError && !matchedSystemError && !isExponential) {\n        return { errorToThrow: responseError };\n      }\n\n      // Exponentially increase the delay each time\n      const exponentialDelay = retryAfterInMs * Math.pow(2, retryCount);\n      // Don't let the delay exceed the maximum\n      const clampedExponentialDelay = Math.min(maxRetryInterval, exponentialDelay);\n      // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n      // that retries across multiple clients don't occur simultaneously.\n      retryAfterInMs =\n        clampedExponentialDelay / 2 + getRandomIntegerInclusive(0, clampedExponentialDelay / 2);\n      return { retryAfterInMs };\n    },\n  };\n}\n\n/**\n * A response is a retry response if it has status codes:\n * - 408, or\n * - Greater or equal than 500, except for 501 and 505.\n */\nexport function isExponentialRetryResponse(response?: PipelineResponse): boolean {\n  return Boolean(\n    response &&\n      response.status !== undefined &&\n      (response.status >= 500 || response.status === 408) &&\n      response.status !== 501 &&\n      response.status !== 505,\n  );\n}\n\n/**\n * Determines whether an error from a pipeline response was triggered in the network layer.\n */\nexport function isSystemError(err?: RestError): boolean {\n  if (!err) {\n    return false;\n  }\n  return (\n    err.code === \"ETIMEDOUT\" ||\n    err.code === \"ESOCKETTIMEDOUT\" ||\n    err.code === \"ECONNREFUSED\" ||\n    err.code === \"ECONNRESET\" ||\n    err.code === \"ENOENT\" ||\n    err.code === \"ENOTFOUND\"\n  );\n}\n"]}