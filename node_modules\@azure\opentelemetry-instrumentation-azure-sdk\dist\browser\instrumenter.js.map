{"version": 3, "file": "instrumenter.js", "sourceRoot": "", "sources": ["../../src/instrumenter.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,oBAAoB,EACpB,OAAO,EACP,oBAAoB,EACpB,oBAAoB,EACpB,KAAK,GACN,MAAM,oBAAoB,CAAC;AAO5B,OAAO,EAAE,yBAAyB,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAEjF,OAAO,EAAE,wBAAwB,EAAE,MAAM,kBAAkB,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,oGAAoG;AACpG,MAAM,CAAC,MAAM,UAAU,GAAG,IAAI,yBAAyB,EAAE,CAAC;AAE1D,MAAM,OAAO,yBAAyB;IACpC,SAAS,CACP,IAAY,EACZ,WAAoC;QAEpC,IAAI,GAAG,GAAG,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,cAAc,KAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1D,IAAI,IAAU,CAAC;QAEf,IAAI,eAAe,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAC9C,sDAAsD;YACtD,IAAI,GAAG,KAAK,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,IAAI,GAAG,KAAK;iBACT,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,cAAc,CAAC;iBAC9D,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;YAEpD,IACE,eAAe,CAAC,sCAAsC,CAAC;gBACvD,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EACrC,CAAC;gBACD,2BAA2B;gBAC3B,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,IAAI,wBAAwB,CAAC,IAAI,CAAC;YACxC,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;SACzC,CAAC;IACJ,CAAC;IACD,WAAW,CAIT,cAA8B,EAC9B,QAAkB,EAClB,GAAG,YAA0B;QAE7B,OAAO,OAAO,CAAC,IAAI,CACjB,cAAc,EACd,QAAQ;QACR,4DAA4D,CAAC,SAAS,EACtE,GAAG,YAAY,CAChB,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,iBAAyB;QAC9C,OAAO,UAAU,CAAC,OAAO,CACvB,OAAO,CAAC,MAAM,EAAE,EAChB,EAAE,WAAW,EAAE,iBAAiB,EAAE,EAClC,oBAAoB,CACrB,CAAC;IACJ,CAAC;IAED,oBAAoB,CAAC,cAA+B;QAClD,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,UAAU,CAAC,MAAM,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,oBAAoB,CAAC,CAAC;QACrF,OAAO,OAAO,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Span } from \"@opentelemetry/api\";\nimport {\n  INVALID_SPAN_CONTEXT,\n  context,\n  defaultTextMapGetter,\n  defaultTextMapSetter,\n  trace,\n} from \"@opentelemetry/api\";\nimport type {\n  Instrumenter,\n  InstrumenterSpanOptions,\n  TracingContext,\n  TracingSpan,\n} from \"@azure/core-tracing\";\nimport { W3CTraceContextPropagator, suppressTracing } from \"@opentelemetry/core\";\n\nimport { OpenTelemetrySpanWrapper } from \"./spanWrapper.js\";\nimport { envVarToBoolean } from \"./configuration.js\";\nimport { toSpanOptions } from \"./transformations.js\";\n\n// While default propagation is user-configurable, Azure services always use the W3C implementation.\nexport const propagator = new W3CTraceContextPropagator();\n\nexport class OpenTelemetryInstrumenter implements Instrumenter {\n  startSpan(\n    name: string,\n    spanOptions: InstrumenterSpanOptions,\n  ): { span: TracingSpan; tracingContext: TracingContext } {\n    let ctx = spanOptions?.tracingContext || context.active();\n    let span: Span;\n\n    if (envVarToBoolean(\"AZURE_TRACING_DISABLED\")) {\n      // disable only our spans but not any downstream spans\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else {\n      // Create our span\n      span = trace\n        .getTracer(spanOptions.packageName, spanOptions.packageVersion)\n        .startSpan(name, toSpanOptions(spanOptions), ctx);\n\n      if (\n        envVarToBoolean(\"AZURE_HTTP_TRACING_CHILDREN_DISABLED\") &&\n        name.toUpperCase().startsWith(\"HTTP\")\n      ) {\n        // disable downstream spans\n        ctx = suppressTracing(ctx);\n      }\n    }\n\n    return {\n      span: new OpenTelemetrySpanWrapper(span),\n      tracingContext: trace.setSpan(ctx, span),\n    };\n  }\n  withContext<\n    CallbackArgs extends unknown[],\n    Callback extends (...args: CallbackArgs) => ReturnType<Callback>,\n  >(\n    tracingContext: TracingContext,\n    callback: Callback,\n    ...callbackArgs: CallbackArgs\n  ): ReturnType<Callback> {\n    return context.with(\n      tracingContext,\n      callback,\n      /** Assume caller will bind `this` or use arrow functions */ undefined,\n      ...callbackArgs,\n    );\n  }\n\n  parseTraceparentHeader(traceparentHeader: string): TracingContext {\n    return propagator.extract(\n      context.active(),\n      { traceparent: traceparentHeader },\n      defaultTextMapGetter,\n    );\n  }\n\n  createRequestHeaders(tracingContext?: TracingContext): Record<string, string> {\n    const headers: Record<string, string> = {};\n    propagator.inject(tracingContext || context.active(), headers, defaultTextMapSetter);\n    return headers;\n  }\n}\n"]}