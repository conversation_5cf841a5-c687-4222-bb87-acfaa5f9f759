/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
export declare class ShadowTreeWalker implements TreeWalker {
    readonly filter: NodeFilter | null;
    readonly root: Node;
    readonly whatToShow: number;
    private _doc;
    private _walkerStack;
    private _currentNode;
    private _currentSetFor;
    constructor(doc: Document, root: Node, whatToShow?: number, filter?: NodeFilter | null);
    private _acceptNode;
    get currentNode(): Node;
    set currentNode(node: Node);
    firstChild(): Node | null;
    lastChild(): Node | null;
    nextNode(): Node | null;
    previousNode(): Node | null;
    nextSibling(): Node | null;
    previousSibling(): Node | null;
    parentNode(): Node | null;
}
export declare function createShadowTreeWalker(doc: Document, root: Node, whatToShow?: number, filter?: NodeFilter | null): ShadowTreeWalker;
