# Change Log - @fluentui/keyboard-keys

This log was last generated on Mon, 11 Nov 2024 09:55:28 GMT and should not be manually modified.

<!-- Start content -->

## [9.0.8](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.8)

Mon, 11 Nov 2024 09:55:28 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.7..@fluentui/keyboard-keys_v9.0.8)

### Patches

- chore: replace npm-scripts and just-scrtips with nx inferred tasks ([PR #33074](https://github.com/microsoft/fluentui/pull/33074) by <EMAIL>)

## [9.0.7](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.7)

Thu, 09 Nov 2023 17:29:49 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.6..@fluentui/keyboard-keys_v9.0.7)

### Patches

- chore: use package.json#files setup instead of npmignore for all v9 libraries ([PR #29734](https://github.com/microsoft/fluentui/pull/29734) by <EMAIL>)

## [9.0.6](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.6)

Tue, 26 Sep 2023 17:49:01 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.5..@fluentui/keyboard-keys_v9.0.6)

### Patches

- chore: trigger manual version bump after broken release ([PR #29303](https://github.com/microsoft/fluentui/pull/29303) by <EMAIL>)

## [9.0.5](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.5)

Tue, 26 Sep 2023 15:32:07 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.4..@fluentui/keyboard-keys_v9.0.5)

### Patches

- fix: bump swc core to mitigate transpilation memory leaks ([PR #29253](https://github.com/microsoft/fluentui/pull/29253) by <EMAIL>)

## [9.0.4](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.4)

Tue, 05 Sep 2023 13:29:14 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.3..@fluentui/keyboard-keys_v9.0.4)

### Patches

- bumps @swc/helpers version to 0.5.1 ([PR #28989](https://github.com/microsoft/fluentui/pull/28989) by <EMAIL>)

## [9.0.3](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.3)

Fri, 12 May 2023 20:28:09 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.2..@fluentui/keyboard-keys_v9.0.3)

### Patches

- chore: exclude .swcrc from being published ([PR #27740](https://github.com/microsoft/fluentui/pull/27740) by <EMAIL>)

## [9.0.2](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.2)

Tue, 21 Mar 2023 21:23:19 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.1..@fluentui/keyboard-keys_v9.0.2)

### Patches

- chore: migrate to swc transpilation approach. ([PR #27250](https://github.com/microsoft/fluentui/pull/27250) by <EMAIL>)
- fix: add node field to package.json exports map. ([PR #27154](https://github.com/microsoft/fluentui/pull/27154) by <EMAIL>)

## [9.0.1](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.1)

Fri, 11 Nov 2022 14:57:50 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0..@fluentui/keyboard-keys_v9.0.1)

### Patches

- fix: create valid export maps ([PR #25558](https://github.com/microsoft/fluentui/pull/25558) by <EMAIL>)

## [9.0.0](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0)

Tue, 28 Jun 2022 15:14:09 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-rc.6..@fluentui/keyboard-keys_v9.0.0)

### Patches

- feat: Initial 9.0.0 release ([PR #23733](https://github.com/microsoft/fluentui/pull/23733) by <EMAIL>)

## [9.0.0-rc.6](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-rc.6)

Thu, 05 May 2022 18:26:28 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-rc.5..@fluentui/keyboard-keys_v9.0.0-rc.6)

### Changes

- feat: ship rolluped only dts ([PR #22823](https://github.com/microsoft/fluentui/pull/22823) by <EMAIL>)

## [9.0.0-rc.5](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-rc.5)

Wed, 04 May 2022 13:26:36 GMT 
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-rc.4..@fluentui/keyboard-keys_v9.0.0-rc.5)

### Changes

- remove star exports ([PR #22681](https://github.com/microsoft/fluentui/pull/22681) by <EMAIL>)

## [9.0.0-rc.4](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-rc.4)

Fri, 04 Mar 2022 05:17:32 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-rc.3..@fluentui/keyboard-keys_v9.0.0-rc.4)

### Changes

- Adding explicit export maps on all consumer packages for FUIR 8 and 9. ([PR #21508](https://github.com/microsoft/fluentui/pull/21508) by <EMAIL>)
- keyboard-keys: Adding missing tslib dependency. ([PR #21947](https://github.com/microsoft/fluentui/pull/21947) by <EMAIL>)

## [9.0.0-rc.3](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-rc.3)

Fri, 18 Feb 2022 13:35:37 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-rc.1..@fluentui/keyboard-keys_v9.0.0-rc.3)

### Changes

- fix: Source maps contain original source code ([PR #21690](https://github.com/microsoft/fluentui/pull/21690) by <EMAIL>)

## [9.0.0-rc.1](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-rc.1)

Thu, 10 Feb 2022 08:52:11 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-beta.1..@fluentui/keyboard-keys_v9.0.0-rc.1)

### Changes

- Bump Fluent UI packages to 9.0.0-rc ([PR #21623](https://github.com/microsoft/fluentui/pull/21623) by <EMAIL>)

## [9.0.0-beta.1](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-beta.1)

Wed, 06 Oct 2021 10:37:22 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-alpha.5..@fluentui/keyboard-keys_v9.0.0-beta.1)

### Changes

- Bump all v9 components to beta prerelease tag ([PR #20106](https://github.com/microsoft/fluentui/pull/20106) by <EMAIL>)

## [9.0.0-alpha.5](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-alpha.5)

Fri, 01 Oct 2021 14:13:08 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-alpha.3..@fluentui/keyboard-keys_v9.0.0-alpha.5)

### Changes

- Bump v9 prerelease versions to rerelease ([PR #20069](https://github.com/microsoft/fluentui/pull/20069) by <EMAIL>)

## [9.0.0-alpha.3](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-alpha.3)

Fri, 20 Aug 2021 07:37:28 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-alpha.2..@fluentui/keyboard-keys_v9.0.0-alpha.3)

### Changes

- Update .npmignore ([PR #19441](https://github.com/microsoft/fluentui/pull/19441) by <EMAIL>)

## [9.0.0-alpha.2](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-alpha.2)

Tue, 03 Aug 2021 07:39:30 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-alpha.1..@fluentui/keyboard-keys_v9.0.0-alpha.2)

### Patches

- Bump @fluentui/eslint-plugin to v1.3.3 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #19169](https://github.com/microsoft/fluentui/pull/19169) by <EMAIL>)

## [9.0.0-alpha.1](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-alpha.1)

Tue, 20 Jul 2021 22:23:17 GMT
[Compare changes](https://github.com/microsoft/fluentui/compare/@fluentui/keyboard-keys_v9.0.0-alpha.0..@fluentui/keyboard-keys_v9.0.0-alpha.1)

### Changes

- Publish package ([PR #19016](https://github.com/microsoft/fluentui/pull/19016) by <EMAIL>)

## [9.0.0-alpha.0](https://github.com/microsoft/fluentui/tree/@fluentui/keyboard-keys_v9.0.0-alpha.0)

Fri, 09 Jul 2021 07:39:31 GMT

### Patches

- Bump @fluentui/eslint-plugin to v1.3.2 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
- Bump @fluentui/scripts to v1.0.0 ([PR #18808](https://github.com/microsoft/fluentui/pull/18808) by <EMAIL>)
