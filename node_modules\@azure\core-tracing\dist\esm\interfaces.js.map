{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A narrower version of TypeScript 4.5's Awaited type which Recursively\n * unwraps the \"awaited type\", emulating the behavior of `await`.\n */\nexport type Resolved<T> = T extends { then(onfulfilled: infer F): any } // `await` only unwraps object types with a callable `then`. Non-object types are not unwrapped\n  ? F extends (value: infer V) => any // if the argument to `then` is callable, extracts the first argument\n    ? Resolved<V> // recursively unwrap the value\n    : never // the argument to `then` was not callable\n  : T; // non-object or non-thenable\n\n/**\n * Represents a client that can integrate with the currently configured {@link Instrumenter}.\n *\n * Create an instance using {@link createTracingClient}.\n */\nexport interface TracingClient {\n  /**\n   * Wraps a callback in a tracing span, calls the callback, and closes the span.\n   *\n   * This is the primary interface for using Tracing and will handle error recording as well as setting the status on the span.\n   *\n   * Both synchronous and asynchronous functions will be awaited in order to reflect the result of the callback on the span.\n   *\n   * Example:\n   *\n   * ```ts snippet:ReadmeSampleWithSpanExample\n   * import { createTracingClient } from \"@azure/core-tracing\";\n   *\n   * const tracingClient = createTracingClient({\n   *   namespace: \"test.namespace\",\n   *   packageName: \"test-package\",\n   *   packageVersion: \"1.0.0\",\n   * });\n   *\n   * const options = {};\n   *\n   * const myOperationResult = await tracingClient.withSpan(\n   *   \"myClassName.myOperationName\",\n   *   options,\n   *   (updatedOptions) => {\n   *     // Do something with the updated options.\n   *     return \"myOperationResult\";\n   *   },\n   * );\n   * ```\n   * @param name - The name of the span. By convention this should be `${className}.${methodName}`.\n   * @param operationOptions - The original options passed to the method. The callback will receive these options with the newly created {@link TracingContext}.\n   * @param callback - The callback to be invoked with the updated options and newly created {@link TracingSpan}.\n   */\n  withSpan<\n    Options extends { tracingOptions?: OperationTracingOptions },\n    Callback extends (\n      updatedOptions: Options,\n      span: Omit<TracingSpan, \"end\">,\n    ) => ReturnType<Callback>,\n  >(\n    name: string,\n    operationOptions: Options,\n    callback: Callback,\n    spanOptions?: TracingSpanOptions,\n  ): Promise<Resolved<ReturnType<Callback>>>;\n  /**\n   * Starts a given span but does not set it as the active span.\n   *\n   * You must end the span using {@link TracingSpan.end}.\n   *\n   * Most of the time you will want to use {@link withSpan} instead.\n   *\n   * @param name - The name of the span. By convention this should be `${className}.${methodName}`.\n   * @param operationOptions - The original operation options.\n   * @param spanOptions - The options to use when creating the span.\n   *\n   * @returns A {@link TracingSpan} and the updated operation options.\n   */\n  startSpan<Options extends { tracingOptions?: OperationTracingOptions }>(\n    name: string,\n    operationOptions?: Options,\n    spanOptions?: TracingSpanOptions,\n  ): {\n    span: TracingSpan;\n    updatedOptions: OptionsWithTracingContext<Options>;\n  };\n  /**\n   * Wraps a callback with an active context and calls the callback.\n   * Depending on the implementation, this may set the globally available active context.\n   *\n   * Useful when you want to leave the boundaries of the SDK (make a request or callback to user code) and are unable to use the {@link withSpan} API.\n   *\n   * @param context - The {@link TracingContext} to use as the active context in the scope of the callback.\n   * @param callback - The callback to be invoked with the given context set as the globally active context.\n   * @param callbackArgs - The callback arguments.\n   */\n  withContext<\n    CallbackArgs extends unknown[],\n    Callback extends (...args: CallbackArgs) => ReturnType<Callback>,\n  >(\n    context: TracingContext,\n    callback: Callback,\n    ...callbackArgs: CallbackArgs\n  ): ReturnType<Callback>;\n\n  /**\n   * Parses a traceparent header value into a {@link TracingSpanContext}.\n   *\n   * @param traceparentHeader - The traceparent header to parse.\n   * @returns An implementation-specific identifier for the span.\n   */\n  parseTraceparentHeader(traceparentHeader: string): TracingContext | undefined;\n\n  /**\n   * Creates a set of request headers to propagate tracing information to a backend.\n   *\n   * @param tracingContext - The context containing the span to propagate.\n   * @returns The set of headers to add to a request.\n   */\n  createRequestHeaders(tracingContext?: TracingContext): Record<string, string>;\n}\n\n/**\n * Options that can be passed to {@link createTracingClient}\n */\nexport interface TracingClientOptions {\n  /** The value of the az.namespace tracing attribute on newly created spans. */\n  namespace: string;\n  /** The name of the package invoking this trace. */\n  packageName: string;\n  /** An optional version of the package invoking this trace. */\n  packageVersion?: string;\n}\n\n/** The kind of span. */\nexport type TracingSpanKind = \"client\" | \"server\" | \"producer\" | \"consumer\" | \"internal\";\n\n/** Options used to configure the newly created span. */\nexport interface TracingSpanOptions {\n  /** The kind of span. Implementations should default this to \"client\". */\n  spanKind?: TracingSpanKind;\n  /** A collection of {@link TracingSpanLink} to link to this span. */\n  spanLinks?: TracingSpanLink[];\n  /** Initial set of attributes to set on a span. */\n  spanAttributes?: { [key: string]: unknown };\n}\n\n/** A pointer from the current {@link TracingSpan} to another span in the same or a different trace. */\nexport interface TracingSpanLink {\n  /** The {@link TracingContext} containing the span context to link to. */\n  tracingContext: TracingContext;\n  /** A set of attributes on the link. */\n  attributes?: { [key: string]: unknown };\n}\n\n/**\n * Represents an implementation agnostic instrumenter.\n */\nexport interface Instrumenter {\n  /**\n   * Creates a new {@link TracingSpan} with the given name and options and sets it on a new context.\n   * @param name - The name of the span. By convention this should be `${className}.${methodName}`.\n   * @param spanOptions - The options to use when creating the span.\n   *\n   * @returns A {@link TracingSpan} that can be used to end the span, and the context this span has been set on.\n   */\n  startSpan(\n    name: string,\n    spanOptions: InstrumenterSpanOptions,\n  ): { span: TracingSpan; tracingContext: TracingContext };\n  /**\n   * Wraps a callback with an active context and calls the callback.\n   * Depending on the implementation, this may set the globally available active context.\n   *\n   * @param context - The {@link TracingContext} to use as the active context in the scope of the callback.\n   * @param callback - The callback to be invoked with the given context set as the globally active context.\n   * @param callbackArgs - The callback arguments.\n   */\n  withContext<\n    CallbackArgs extends unknown[],\n    Callback extends (...args: CallbackArgs) => ReturnType<Callback>,\n  >(\n    context: TracingContext,\n    callback: Callback,\n    ...callbackArgs: CallbackArgs\n  ): ReturnType<Callback>;\n\n  /**\n   * Provides an implementation-specific method to parse a {@link https://www.w3.org/TR/trace-context/#traceparent-header}\n   * into a {@link TracingSpanContext} which can be used to link non-parented spans together.\n   */\n  parseTraceparentHeader(traceparentHeader: string): TracingContext | undefined;\n  /**\n   * Provides an implementation-specific method to serialize a {@link TracingSpan} to a set of headers.\n   * @param tracingContext - The context containing the span to serialize.\n   */\n  createRequestHeaders(tracingContext?: TracingContext): Record<string, string>;\n}\n\n/**\n * Options passed to {@link Instrumenter.startSpan} as a superset of {@link TracingSpanOptions}.\n */\nexport interface InstrumenterSpanOptions extends TracingSpanOptions {\n  /** The name of the package invoking this trace. */\n  packageName: string;\n  /** The version of the package invoking this trace. */\n  packageVersion?: string;\n  /** The current tracing context. Defaults to an implementation-specific \"active\" context. */\n  tracingContext?: TracingContext;\n}\n\n/**\n * Status representing a successful operation that can be sent to {@link TracingSpan.setStatus}\n */\nexport type SpanStatusSuccess = { status: \"success\" };\n\n/**\n * Status representing an error that can be sent to {@link TracingSpan.setStatus}\n */\nexport type SpanStatusError = { status: \"error\"; error?: Error | string };\n\n/**\n * Represents the statuses that can be passed to {@link TracingSpan.setStatus}.\n *\n * By default, all spans will be created with status \"unset\".\n */\nexport type SpanStatus = SpanStatusSuccess | SpanStatusError;\n\n/**\n * Represents options you can pass to {@link TracingSpan.addEvent}.\n */\nexport interface AddEventOptions {\n  /**\n   * A set of attributes to attach to the event.\n   */\n  attributes?: Record<string, unknown>;\n  /**\n   * The start time of the event.\n   */\n  startTime?: Date;\n}\n\n/**\n * Represents an implementation agnostic tracing span.\n */\nexport interface TracingSpan {\n  /**\n   * Sets the status of the span. When an error is provided, it will be recorded on the span as well.\n   *\n   * @param status - The {@link SpanStatus} to set on the span.\n   */\n  setStatus(status: SpanStatus): void;\n\n  /**\n   * Sets a given attribute on a span.\n   *\n   * @param name - The attribute's name.\n   * @param value - The attribute's value to set. May be any non-nullish value.\n   */\n  setAttribute(name: string, value: unknown): void;\n\n  /**\n   * Ends the span.\n   */\n  end(): void;\n\n  /**\n   * Records an exception on a {@link TracingSpan} without modifying its status.\n   *\n   * When recording an unhandled exception that should fail the span, please use {@link TracingSpan.setStatus} instead.\n   *\n   * @param exception - The exception to record on the span.\n   *\n   */\n  recordException(exception: Error | string): void;\n\n  /**\n   * Returns true if this {@link TracingSpan} is recording information.\n   *\n   * Depending on the span implementation, this may return false if the span is not being sampled.\n   */\n  isRecording(): boolean;\n\n  /**\n   * Adds an event to the span.\n   */\n  addEvent?(name: string, options?: AddEventOptions): void;\n}\n\n/** An immutable context bag of tracing values for the current operation. */\nexport interface TracingContext {\n  /**\n   * Sets a given object on a context.\n   * @param key - The key of the given context value.\n   * @param value - The value to set on the context.\n   *\n   * @returns - A new context with the given value set.\n   */\n  setValue(key: symbol, value: unknown): TracingContext;\n  /**\n   * Gets an object from the context if it exists.\n   * @param key - The key of the given context value.\n   *\n   * @returns - The value of the given context value if it exists, otherwise `undefined`.\n   */\n  getValue(key: symbol): unknown;\n  /**\n   * Deletes an object from the context if it exists.\n   * @param key - The key of the given context value to delete.\n   */\n  deleteValue(key: symbol): TracingContext;\n}\n\n/**\n * Tracing options to set on an operation.\n */\nexport interface OperationTracingOptions {\n  /** The context to use for created Tracing Spans. */\n  tracingContext?: TracingContext;\n}\n\n/**\n * A utility type for when we know a TracingContext has been set\n * as part of an operation's options.\n */\nexport type OptionsWithTracingContext<\n  Options extends { tracingOptions?: OperationTracingOptions },\n> = Options & {\n  tracingOptions: {\n    tracingContext: TracingContext;\n  };\n};\n"]}