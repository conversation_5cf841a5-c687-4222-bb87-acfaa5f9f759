/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
export declare class OutlineAPI implements Types.OutlineAPI {
    private _tabster;
    private _win;
    private _updateTimer;
    private _outlinedElement;
    private _curPos;
    private _isVisible;
    private _curOutlineElements;
    private _allOutlineElements;
    private _fullScreenElement;
    private _fullScreenEventName;
    private _fullScreenElementName;
    constructor(tabster: Types.TabsterCore);
    private _init;
    setup(props?: Partial<Types.OutlineProps>): void;
    dispose(): void;
    private _onFullScreenChanged;
    private _onKeyboardNavigationStateChanged;
    private _shouldShowCustomOutline;
    private _onFocus;
    private _updateElement;
    private _onScroll;
    private _updateOutline;
    private _setVisibility;
    private _setOutlinePosition;
    private _getDOM;
    private _removeDOM;
    private static _isParentChild;
}
