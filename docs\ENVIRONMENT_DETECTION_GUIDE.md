# Environment Detection Implementation Guide

## Overview

This guide provides a complete implementation pattern for automatic environment detection in Power Platform Custom Controls (PCF). The system automatically switches between development (localhost) and production (CRM) modes without manual configuration.

## Key Benefits

- **Zero Configuration**: Automatic environment detection
- **Safe Development**: Prevents accidental CRM writes during development
- **Seamless Deployment**: Works in both localhost and CRM environments
- **Data Protection**: Uses mock data locally, live data in production
- **Error Resilience**: Graceful fallbacks and error handling

## Implementation Pattern

### 1. Core Environment Detection Function

Create a utility function to detect the current environment:

```typescript
// utils/environmentUtils.ts
export const isDevEnvironment = (): boolean => {
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        return hostname === 'localhost' || 
               hostname === '127.0.0.1' || 
               hostname.includes('localhost');
    }
    return false;
};
```

### 2. Context-Aware Detection (Alternative)

For PCF components, you can also check for CRM context availability:

```typescript
export const isDevEnvironment = (context?: ComponentFramework.Context<any>): boolean => {
    return !context || 
           window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1';
};
```

### 3. Conditional Data Loading

Implement a data service that switches between mock and live data:

```typescript
// utils/dataService.ts
export const loadData = async (
    context?: ComponentFramework.Context<any>, 
    entityId?: string | null
): Promise<DataResult> => {
    
    if (isDevEnvironment() || !context) {
        console.log('🔧 Development Mode: Using static test data');
        return {
            data: mockData,
            connections: mockConnections
        };
    } else {
        console.log('🚀 Production Mode: Fetching live data from CRM');
        const crmService = new CRMDataService(context, entityId);
        try {
            const data = await crmService.fetchLiveData();
            return {
                data: data,
                connections: crmService.generateConnections(data)
            };
        } catch (error) {
            console.warn('⚠️ CRM fetch failed, falling back to mock data:', error);
            return {
                data: mockData,
                connections: mockConnections
            };
        }
    }
};
```

### 4. Protected CRM Operations

Wrap all CRM write operations with environment checks:

```typescript
// Protect CRM updates
const updateCRMData = (data: any, context?: ComponentFramework.Context<any>) => {
    if (!isDevEnvironment(context) && context) {
        console.log('💾 Updating CRM with new data');
        // Perform CRM update
        context.webAPI.updateRecord(entityName, recordId, data);
    } else {
        console.log('🔧 Development Mode: Skipping CRM update');
    }
};
```

### 5. Entity ID Detection

For context-aware components, detect the current record ID:

```typescript
// index.ts - PCF Component
public init(
    context: ComponentFramework.Context<IInputs>,
    notifyOutputChanged: () => void,
    state: ComponentFramework.Dictionary,
    container: HTMLDivElement
): void {
    this.context = context;
    
    // Get current record ID if available
    try {
        this._entityId = (context.mode as any).contextInfo?.entityId || null;
        console.log('📋 Current Record ID:', this._entityId);
    } catch (error) {
        console.log('🔧 Development Mode: No record context available');
        this._entityId = null;
    }
}
```

## Environment-Specific Behaviors

### Development Environment (Localhost)

**Detection Criteria:**
- `window.location.hostname === 'localhost'`
- `window.location.hostname === '127.0.0.1'`
- `hostname.includes('localhost')`
- Missing CRM context

**Behaviors:**
- ✅ Uses static mock data
- ❌ No CRM write operations
- 🔧 Console: "Development Mode" messages
- 📊 Full functionality with test data

### Production Environment (CRM)

**Detection Criteria:**
- Valid CRM context available
- Hostname is not localhost/127.0.0.1

**Behaviors:**
- ✅ Fetches live data from CRM
- ✅ Performs CRM write operations
- 🚀 Console: "Production Mode" messages
- 📋 Uses current record context

## Implementation Checklist

### ✅ Setup Phase
- [ ] Create environment detection utility
- [ ] Implement mock data structures
- [ ] Create CRM service class
- [ ] Add conditional data loading

### ✅ Protection Phase
- [ ] Wrap all CRM writes with environment checks
- [ ] Add console logging for debugging
- [ ] Implement error handling and fallbacks
- [ ] Test both environments

### ✅ Testing Phase
- [ ] Verify localhost uses mock data
- [ ] Confirm CRM writes are blocked in development
- [ ] Test production deployment
- [ ] Validate error handling

## Code Examples

### Complete Data Service Implementation

```typescript
export class DataService {
    private context: ComponentFramework.Context<any> | null;
    private entityId: string | null;

    constructor(context?: ComponentFramework.Context<any>, entityId?: string | null) {
        this.context = context || null;
        this.entityId = entityId || null;
    }

    async loadData(): Promise<DataResult> {
        if (this.isDevMode()) {
            return this.loadMockData();
        } else {
            return this.loadCRMData();
        }
    }

    private isDevMode(): boolean {
        return !this.context || 
               window.location.hostname === 'localhost' ||
               window.location.hostname === '127.0.0.1';
    }

    private async loadMockData(): Promise<DataResult> {
        console.log('🔧 Loading mock data for development');
        return { data: mockData, success: true };
    }

    private async loadCRMData(): Promise<DataResult> {
        console.log('🚀 Loading live data from CRM');
        try {
            const response = await this.context!.webAPI.retrieveMultipleRecords(
                'your_entity', 
                `?$filter=_parent_id_value eq ${this.entityId}`
            );
            return { data: response.entities, success: true };
        } catch (error) {
            console.warn('⚠️ CRM load failed, using fallback data');
            return { data: mockData, success: false };
        }
    }

    async saveData(data: any): Promise<boolean> {
        if (this.isDevMode()) {
            console.log('🔧 Development Mode: Simulating save operation');
            return true;
        } else {
            console.log('💾 Saving data to CRM');
            try {
                await this.context!.webAPI.updateRecord('your_entity', this.entityId!, data);
                return true;
            } catch (error) {
                console.error('❌ CRM save failed:', error);
                return false;
            }
        }
    }
}
```

## Best Practices

### 🎯 Do's
- Always check environment before CRM operations
- Use descriptive console logging with emojis
- Implement graceful fallbacks for CRM failures
- Test both environments thoroughly
- Use TypeScript for type safety

### ❌ Don'ts
- Never hardcode environment flags
- Don't skip error handling
- Avoid complex environment detection logic
- Don't forget to test production deployment
- Never commit sensitive CRM data in mock files

## Troubleshooting

### Common Issues

**Issue**: Component uses mock data in production
- **Cause**: CRM context not properly passed
- **Solution**: Verify context is passed to all components

**Issue**: CRM operations fail silently
- **Cause**: Missing error handling
- **Solution**: Add try-catch blocks and logging

**Issue**: Environment detection inconsistent
- **Cause**: Multiple detection functions
- **Solution**: Use single source of truth for environment detection

### Debug Commands

```typescript
// Add to component for debugging
console.log('Environment Debug Info:', {
    hostname: window.location.hostname,
    hasContext: !!context,
    entityId: entityId,
    isDevMode: isDevEnvironment(context)
});
```

## Deployment Notes

### Development Deployment
1. Run `npm run start:watch`
2. Navigate to `http://localhost:8181`
3. Verify mock data is used
4. Check console for "Development Mode" messages

### Production Deployment
1. Build and deploy to CRM environment
2. Verify live data is fetched
3. Test CRM write operations
4. Monitor console for "Production Mode" messages

---

**Note**: This pattern works for any Power Platform PCF component and can be adapted for other web applications that need environment-aware behavior.
