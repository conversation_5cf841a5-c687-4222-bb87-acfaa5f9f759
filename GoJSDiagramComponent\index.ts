import { IInputs, IOutputs } from "./generated/ManifestTypes";
import { createRoot, Root } from 'react-dom/client';
import React from 'react';
import { GoJSDiagramComponent as DiagramComponent } from './components/GoJSDiagramComponent';
import { getCurrentEntityId, getEnvironmentInfo } from './utils/environmentUtils';

import './styles/DiagramComponent.css';

export class GoJSDiagramComponent implements ComponentFramework.StandardControl<IInputs, IOutputs> {
    private root: Root;
    private context: ComponentFramework.Context<IInputs>;
    private entityId: string | null = null;

    /**
     * Empty constructor.
     */
    constructor() {
        // Empty
    }

    /**
     * Used to initialize the control instance. Controls can kick off remote server calls and other initialization actions here.
     * Data-set values are not initialized here, use updateView.
     * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to property names defined in the manifest, as well as utility functions.
     * @param notifyOutputChanged A callback method to alert the framework that the control has new outputs ready to be retrieved asynchronously.
     * @param state A piece of data that persists in one session for a single user. Can be set at any point in a controls life cycle by calling 'setControlState' in the Mode interface.
     * @param container If a control is marked control-type='standard', it will receive an empty div element within which it can render its content.
     */
    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        state: ComponentFramework.Dictionary,
        container: HTMLDivElement
    ): void {
        // Store context for later use
        this.context = context;

        // Extract entity ID from context
        this.entityId = getCurrentEntityId(context);

        // Log environment information for debugging
        getEnvironmentInfo(context);

        // Set container styling
        container.style.height = "100%";
        container.style.width = "100%";
        container.className = "diagram-container";

        // Create React root
        this.root = createRoot(container);

        // Initial render
        this.renderComponent();
    }

    /**
     * Called when any value in the property bag has changed. This includes field values, data-sets, global values such as container height and width, offline status, control metadata values such as label, visible, etc.
     * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to names defined in the manifest, as well as utility functions
     */
    public updateView(context: ComponentFramework.Context<IInputs>): void {
        // Update context
        this.context = context;

        // Update entity ID
        this.entityId = getCurrentEntityId(context);

        // Re-render component with updated data
        this.renderComponent();
    }

    /**
     * It is called by the framework prior to a control receiving new data.
     * @returns an object based on nomenclature defined in manifest, expecting object[s] for property marked as "bound" or "output"
     */
    public getOutputs(): IOutputs {
        return {};
    }

    /**
     * Called when the control is to be removed from the DOM tree. Controls should use this call for cleanup.
     * i.e. cancelling any pending remote calls, removing listeners, etc.
     */
    public destroy(): void {
        if (this.root) {
            this.root.unmount();
        }
    }

    /**
     * Renders the React component
     */
    private renderComponent(): void {
        // Get dimensions from context mode with proper fallbacks
        let width = this.context.mode.allocatedWidth;
        let height = this.context.mode.allocatedHeight;

        // Handle negative or invalid dimensions
        if (!width || width <= 0) {
            width = 800; // Default width
        }
        if (!height || height <= 0) {
            height = 600; // Default height
        }

        console.log('📐 PCF Control dimensions:', {
            allocatedWidth: this.context.mode.allocatedWidth,
            allocatedHeight: this.context.mode.allocatedHeight,
            finalWidth: width,
            finalHeight: height
        });

        const props = {
            width: width,
            height: height,
            context: this.context,
            entityId: this.entityId
        };

        console.log('🔄 Rendering React component with props:', props);
        this.root.render(React.createElement(DiagramComponent, props));
    }
}
