{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAkClC,OAAO,EAKL,mBAAmB,GACpB,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,yBAAyB,GAI1B,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,qBAAqB,EAA+B,MAAM,sBAAsB,CAAC;AAC1F,OAAO,EAAE,SAAS,EAAyB,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC/E,OAAO,EACL,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACL,sBAAsB,EAEtB,0BAA0B,GAC3B,MAAM,sCAAsC,CAAC;AAC9C,OAAO,EACL,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,wCAAwC,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,aAAa,EAAyB,MAAM,yBAAyB,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACrF,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAClG,OAAO,EACL,cAAc,EACd,kBAAkB,GAEnB,MAAM,8BAA8B,CAAC;AACtC,OAAO,EACL,sBAAsB,EAEtB,0BAA0B,GAC3B,MAAM,sCAAsC,CAAC;AAC9C,OAAO,EACL,qBAAqB,EACrB,yBAAyB,GAE1B,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,WAAW,EAA2B,MAAM,2BAA2B,CAAC;AAMjF,OAAO,EACL,aAAa,EACb,iBAAiB,GAElB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EACL,kBAAkB,GAEnB,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACL,eAAe,EACf,mBAAmB,GAEpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClF,OAAO,EACL,+BAA+B,EAE/B,mCAAmC,GAIpC,MAAM,+CAA+C,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC5E,OAAO,EACL,mCAAmC,EAEnC,uCAAuC,GACxC,MAAM,mDAAmD,CAAC;AAC3D,OAAO,EACL,UAAU,EACV,oBAAoB,GAGrB,MAAM,gBAAgB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\ndeclare global {\n  interface FormData {}\n  interface Blob {}\n  interface File {}\n  interface ReadableStream<R = any> {}\n  interface TransformStream<I = any, O = any> {}\n}\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nexport type {\n  Agent,\n  BodyPart,\n  FormDataMap,\n  FormDataValue,\n  HttpClient,\n  HttpHeaders,\n  HttpMethods,\n  KeyObject,\n  MultipartRequestBody,\n  PipelineRequest,\n  PipelineResponse,\n  PipelineRetryOptions,\n  ProxySettings,\n  PxfObject,\n  RawHttpHeaders,\n  RawHttpHeadersInput,\n  RequestBodyType,\n  SendRequest,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nexport {\n  type AddPolicyOptions as AddPipelineOptions,\n  type PipelinePhase,\n  type PipelinePolicy,\n  type Pipeline,\n  createEmptyPipeline,\n} from \"./pipeline.js\";\nexport {\n  createPipelineFromOptions,\n  type TelemetryOptions,\n  type InternalPipelineOptions,\n  type PipelineOptions,\n} from \"./createPipelineFromOptions.js\";\nexport { createDefaultHttpClient } from \"./defaultHttpClient.js\";\nexport { createHttpHeaders } from \"./httpHeaders.js\";\nexport { createPipelineRequest, type PipelineRequestOptions } from \"./pipelineRequest.js\";\nexport { RestError, type RestErrorOptions, isRestError } from \"./restError.js\";\nexport {\n  decompressResponsePolicy,\n  decompressResponsePolicyName,\n} from \"./policies/decompressResponsePolicy.js\";\nexport {\n  exponentialRetryPolicy,\n  type ExponentialRetryPolicyOptions,\n  exponentialRetryPolicyName,\n} from \"./policies/exponentialRetryPolicy.js\";\nexport {\n  setClientRequestIdPolicy,\n  setClientRequestIdPolicyName,\n} from \"./policies/setClientRequestIdPolicy.js\";\nexport { logPolicy, logPolicyName, type LogPolicyOptions } from \"./policies/logPolicy.js\";\nexport { multipartPolicy, multipartPolicyName } from \"./policies/multipartPolicy.js\";\nexport { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from \"./policies/proxyPolicy.js\";\nexport {\n  redirectPolicy,\n  redirectPolicyName,\n  type RedirectPolicyOptions,\n} from \"./policies/redirectPolicy.js\";\nexport {\n  systemErrorRetryPolicy,\n  type SystemErrorRetryPolicyOptions,\n  systemErrorRetryPolicyName,\n} from \"./policies/systemErrorRetryPolicy.js\";\nexport {\n  throttlingRetryPolicy,\n  throttlingRetryPolicyName,\n  type ThrottlingRetryPolicyOptions,\n} from \"./policies/throttlingRetryPolicy.js\";\nexport { retryPolicy, type RetryPolicyOptions } from \"./policies/retryPolicy.js\";\nexport type {\n  RetryStrategy,\n  RetryInformation,\n  RetryModifiers,\n} from \"./retryStrategies/retryStrategy.js\";\nexport {\n  tracingPolicy,\n  tracingPolicyName,\n  type TracingPolicyOptions,\n} from \"./policies/tracingPolicy.js\";\nexport {\n  defaultRetryPolicy,\n  type DefaultRetryPolicyOptions,\n} from \"./policies/defaultRetryPolicy.js\";\nexport {\n  userAgentPolicy,\n  userAgentPolicyName,\n  type UserAgentPolicyOptions,\n} from \"./policies/userAgentPolicy.js\";\nexport { tlsPolicy, tlsPolicyName } from \"./policies/tlsPolicy.js\";\nexport { formDataPolicy, formDataPolicyName } from \"./policies/formDataPolicy.js\";\nexport {\n  bearerTokenAuthenticationPolicy,\n  type BearerTokenAuthenticationPolicyOptions,\n  bearerTokenAuthenticationPolicyName,\n  type ChallengeCallbacks,\n  type AuthorizeRequestOptions,\n  type AuthorizeRequestOnChallengeOptions,\n} from \"./policies/bearerTokenAuthenticationPolicy.js\";\nexport { ndJsonPolicy, ndJsonPolicyName } from \"./policies/ndJsonPolicy.js\";\nexport {\n  auxiliaryAuthenticationHeaderPolicy,\n  type AuxiliaryAuthenticationHeaderPolicyOptions,\n  auxiliaryAuthenticationHeaderPolicyName,\n} from \"./policies/auxiliaryAuthenticationHeaderPolicy.js\";\nexport {\n  createFile,\n  createFileFromStream,\n  type CreateFileOptions,\n  type CreateFileFromStreamOptions,\n} from \"./util/file.js\";\n"]}