/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "../Types";
import { Subscribable } from "./Subscribable";
export declare class KeyboardNavigationState extends Subscribable<boolean> implements Types.KeyboardNavigationState {
    private _keyborg?;
    constructor(getWindow: Types.GetWindow);
    dispose(): void;
    private _onChange;
    setNavigatingWithKeyboard(isNavigatingWithKeyboard: boolean): void;
    isNavigatingWithKeyboard(): boolean;
}
