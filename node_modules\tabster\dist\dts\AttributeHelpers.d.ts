/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
export declare function getTabsterAttribute(props: Types.TabsterAttributeProps): Types.TabsterDOMAttribute;
export declare function getTabsterAttribute(props: Types.TabsterAttributeProps, plain: true): string;
/**
 * Updates Tabster props object with new props.
 * @param element an element to set data-tabster attribute on.
 * @param props current Tabster props to update.
 * @param newProps new Tabster props to add.
 *  When the value of a property in newProps is undefined, the property
 *  will be removed from the attribute.
 */
export declare function mergeTabsterProps(props: Types.TabsterAttributeProps, newProps: Types.TabsterAttributeProps): void;
/**
 * Sets or updates Tabster attribute of the element.
 * @param element an element to set data-tabster attribute on.
 * @param newProps new Tabster props to set.
 * @param update if true, newProps will be merged with the existing props.
 *  When true and the value of a property in newProps is undefined, the property
 *  will be removed from the attribute.
 */
export declare function setTabsterAttribute(element: HTMLElement, newProps: Types.TabsterAttributeProps, update?: boolean): void;
