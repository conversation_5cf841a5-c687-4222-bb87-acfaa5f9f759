{"version": 3, "sources": ["../src/overflowManager.ts"], "sourcesContent": ["import { DATA_OVERFLOWING, DATA_OVERFLOW_GROUP } from './consts';\nimport { observeResize } from './createResizeObserver';\nimport { debounce } from './debounce';\nimport { createPriorityQueue, PriorityQueue } from './priorityQueue';\nimport type {\n  OverflowGroupState,\n  OverflowItemEntry,\n  OverflowManager,\n  ObserveOptions,\n  OverflowDividerEntry,\n} from './types';\n\n/**\n * @internal\n * @returns overflow manager instance\n */\nexport function createOverflowManager(): OverflowManager {\n  // calls to `offsetWidth or offsetHeight` can happen multiple times in an update\n  // Use a cache to avoid causing too many recalcs and avoid scripting time to meausure sizes\n  const sizeCache = new Map<HTMLElement, number>();\n  let container: HTMLElement | undefined;\n  let overflowMenu: HTMLElement | undefined;\n  // Set as true when resize observer is observing\n  let observing = false;\n  // If true, next update will dispatch to onUpdateOverflow even if queue top states don't change\n  // Initially true to force dispatch on first mount\n  let forceDispatch = true;\n  const options: Required<ObserveOptions> = {\n    padding: 10,\n    overflowAxis: 'horizontal',\n    overflowDirection: 'end',\n    minimumVisible: 0,\n    onUpdateItemVisibility: () => undefined,\n    onUpdateOverflow: () => undefined,\n  };\n\n  const overflowItems: Record<string, OverflowItemEntry> = {};\n  const overflowDividers: Record<string, OverflowDividerEntry> = {};\n  let disposeResizeObserver: () => void = () => null;\n\n  const getNextItem = (queueToDequeue: PriorityQueue<string>, queueToEnqueue: PriorityQueue<string>) => {\n    const nextItem = queueToDequeue.dequeue();\n    queueToEnqueue.enqueue(nextItem);\n    return overflowItems[nextItem];\n  };\n\n  const groupManager = createGroupManager();\n\n  function compareItems(lt: string | null, rt: string | null): number {\n    if (!lt || !rt) {\n      return 0;\n    }\n\n    const lte = overflowItems[lt];\n    const rte = overflowItems[rt];\n\n    // TODO this should not happen but there have been reports of one of these items being undefined\n    // Try to find a consistent repro for this\n    if (!lte || !rte) {\n      return lte ? 1 : -1;\n    }\n\n    if (lte.priority !== rte.priority) {\n      return lte.priority > rte.priority ? 1 : -1;\n    }\n\n    const positionStatusBit =\n      options.overflowDirection === 'end' ? Node.DOCUMENT_POSITION_FOLLOWING : Node.DOCUMENT_POSITION_PRECEDING;\n\n    // eslint-disable-next-line no-bitwise\n    return lte.element.compareDocumentPosition(rte.element) & positionStatusBit ? 1 : -1;\n  }\n\n  function getElementAxisSize(\n    horizontal: 'clientWidth' | 'offsetWidth',\n    vertical: 'clientHeight' | 'offsetHeight',\n    el: HTMLElement,\n  ): number {\n    if (!sizeCache.has(el)) {\n      sizeCache.set(el, options.overflowAxis === 'horizontal' ? el[horizontal] : el[vertical]);\n    }\n\n    return sizeCache.get(el)!;\n  }\n\n  const getOffsetSize = getElementAxisSize.bind(null, 'offsetWidth', 'offsetHeight');\n  const getClientSize = getElementAxisSize.bind(null, 'clientWidth', 'clientHeight');\n\n  const invisibleItemQueue = createPriorityQueue<string>((a, b) => -1 * compareItems(a, b));\n\n  const visibleItemQueue = createPriorityQueue<string>(compareItems);\n\n  function occupiedSize(): number {\n    const totalItemSize = visibleItemQueue\n      .all()\n      .map(id => overflowItems[id].element)\n      .map(getOffsetSize)\n      .reduce((prev, current) => prev + current, 0);\n\n    const totalDividerSize = Object.entries(groupManager.groupVisibility()).reduce(\n      (acc, [id, state]) =>\n        acc + (state !== 'hidden' && overflowDividers[id] ? getOffsetSize(overflowDividers[id].element) : 0),\n      0,\n    );\n\n    const overflowMenuSize = invisibleItemQueue.size() > 0 && overflowMenu ? getOffsetSize(overflowMenu) : 0;\n\n    return totalItemSize + totalDividerSize + overflowMenuSize;\n  }\n\n  const showItem = () => {\n    const item = getNextItem(invisibleItemQueue, visibleItemQueue);\n    options.onUpdateItemVisibility({ item, visible: true });\n\n    if (item.groupId) {\n      groupManager.showItem(item.id, item.groupId);\n\n      if (groupManager.isSingleItemVisible(item.id, item.groupId)) {\n        overflowDividers[item.groupId]?.element.removeAttribute(DATA_OVERFLOWING);\n      }\n    }\n  };\n\n  const hideItem = () => {\n    const item = getNextItem(visibleItemQueue, invisibleItemQueue);\n    options.onUpdateItemVisibility({ item, visible: false });\n\n    if (item.groupId) {\n      if (groupManager.isSingleItemVisible(item.id, item.groupId)) {\n        overflowDividers[item.groupId]?.element.setAttribute(DATA_OVERFLOWING, '');\n      }\n\n      groupManager.hideItem(item.id, item.groupId);\n    }\n  };\n\n  const dispatchOverflowUpdate = () => {\n    const visibleItemIds = visibleItemQueue.all();\n    const invisibleItemIds = invisibleItemQueue.all();\n\n    const visibleItems = visibleItemIds.map(itemId => overflowItems[itemId]);\n    const invisibleItems = invisibleItemIds.map(itemId => overflowItems[itemId]);\n\n    options.onUpdateOverflow({ visibleItems, invisibleItems, groupVisibility: groupManager.groupVisibility() });\n  };\n\n  const processOverflowItems = (): boolean => {\n    if (!container) {\n      return false;\n    }\n    sizeCache.clear();\n\n    const availableSize = getClientSize(container) - options.padding;\n\n    // Snapshot of the visible/invisible state to compare for updates\n    const visibleTop = visibleItemQueue.peek();\n    const invisibleTop = invisibleItemQueue.peek();\n\n    while (compareItems(invisibleItemQueue.peek(), visibleItemQueue.peek()) > 0) {\n      hideItem(); // hide elements whose priority become smaller than the highest priority of the hidden one\n    }\n\n    // Run the show/hide step twice - the first step might not be correct if\n    // it was triggered by a new item being added - new items are always visible by default.\n    for (let i = 0; i < 2; i++) {\n      // Add items until available width is filled - can result in overflow\n      while (\n        (occupiedSize() < availableSize && invisibleItemQueue.size() > 0) ||\n        invisibleItemQueue.size() === 1 // attempt to show the last invisible item hoping it's size does not exceed overflow menu size\n      ) {\n        showItem();\n      }\n\n      // Remove items until there's no more overflow\n      while (occupiedSize() > availableSize && visibleItemQueue.size() > options.minimumVisible) {\n        hideItem();\n      }\n    }\n\n    // only update when the state of visible/invisible items has changed\n    return visibleItemQueue.peek() !== visibleTop || invisibleItemQueue.peek() !== invisibleTop;\n  };\n\n  const forceUpdate: OverflowManager['forceUpdate'] = () => {\n    if (processOverflowItems() || forceDispatch) {\n      forceDispatch = false;\n      dispatchOverflowUpdate();\n    }\n  };\n\n  const update: OverflowManager['update'] = debounce(forceUpdate);\n\n  const observe: OverflowManager['observe'] = (observedContainer, userOptions) => {\n    Object.assign(options, userOptions);\n    observing = true;\n    Object.values(overflowItems).forEach(item => visibleItemQueue.enqueue(item.id));\n\n    container = observedContainer;\n    disposeResizeObserver = observeResize(container, entries => {\n      if (!entries[0] || !container) {\n        return;\n      }\n\n      update();\n    });\n  };\n\n  const addItem: OverflowManager['addItem'] = item => {\n    if (overflowItems[item.id]) {\n      return;\n    }\n\n    overflowItems[item.id] = item;\n\n    // some options can affect priority which are only set on `observe`\n    if (observing) {\n      // Updates to elements might not change the queue tops\n      // i.e. new element is enqueued but the top of the queue stays the same\n      // force a dispatch on the next batched update\n      forceDispatch = true;\n      visibleItemQueue.enqueue(item.id);\n    }\n\n    if (item.groupId) {\n      groupManager.addItem(item.id, item.groupId);\n      item.element.setAttribute(DATA_OVERFLOW_GROUP, item.groupId);\n    }\n\n    update();\n  };\n\n  const addOverflowMenu: OverflowManager['addOverflowMenu'] = el => {\n    overflowMenu = el;\n  };\n\n  const addDivider: OverflowManager['addDivider'] = divider => {\n    if (!divider.groupId || overflowDividers[divider.groupId]) {\n      return;\n    }\n\n    divider.element.setAttribute(DATA_OVERFLOW_GROUP, divider.groupId);\n    overflowDividers[divider.groupId] = divider;\n  };\n\n  const removeOverflowMenu: OverflowManager['removeOverflowMenu'] = () => {\n    overflowMenu = undefined;\n  };\n\n  const removeDivider: OverflowManager['removeDivider'] = groupId => {\n    if (!overflowDividers[groupId]) {\n      return;\n    }\n    const divider = overflowDividers[groupId];\n    if (divider.groupId) {\n      delete overflowDividers[groupId];\n      divider.element.removeAttribute(DATA_OVERFLOW_GROUP);\n    }\n  };\n\n  const removeItem: OverflowManager['removeItem'] = itemId => {\n    if (!overflowItems[itemId]) {\n      return;\n    }\n\n    if (observing) {\n      // We might be removing an item in an overflow which would not affect the tops,\n      // but we need to update anyway to update the overflow menu state\n      forceDispatch = true;\n    }\n\n    const item = overflowItems[itemId];\n    visibleItemQueue.remove(itemId);\n    invisibleItemQueue.remove(itemId);\n\n    if (item.groupId) {\n      groupManager.removeItem(item.id, item.groupId);\n      item.element.removeAttribute(DATA_OVERFLOW_GROUP);\n    }\n\n    sizeCache.delete(item.element);\n    delete overflowItems[itemId];\n    update();\n  };\n\n  const disconnect: OverflowManager['disconnect'] = () => {\n    disposeResizeObserver();\n\n    // reset flags\n    container = undefined;\n    observing = false;\n    forceDispatch = true;\n\n    // clear all entries\n    Object.keys(overflowItems).forEach(itemId => removeItem(itemId));\n    Object.keys(overflowDividers).forEach(dividerId => removeDivider(dividerId));\n    removeOverflowMenu();\n    sizeCache.clear();\n  };\n\n  return {\n    addItem,\n    disconnect,\n    forceUpdate,\n    observe,\n    removeItem,\n    update,\n    addOverflowMenu,\n    removeOverflowMenu,\n    addDivider,\n    removeDivider,\n  };\n}\n\nconst createGroupManager = () => {\n  const groupVisibility: Record<string, OverflowGroupState> = {};\n  const groups: Record<string, { visibleItemIds: Set<string>; invisibleItemIds: Set<string> }> = {};\n  function updateGroupVisibility(groupId: string) {\n    const group = groups[groupId];\n    if (group.invisibleItemIds.size && group.visibleItemIds.size) {\n      groupVisibility[groupId] = 'overflow';\n    } else if (group.visibleItemIds.size === 0) {\n      groupVisibility[groupId] = 'hidden';\n    } else {\n      groupVisibility[groupId] = 'visible';\n    }\n  }\n  function isGroupVisible(groupId: string) {\n    return groupVisibility[groupId] === 'visible' || groupVisibility[groupId] === 'overflow';\n  }\n  return {\n    groupVisibility: () => groupVisibility,\n    isSingleItemVisible(itemId: string, groupId: string) {\n      return (\n        isGroupVisible(groupId) &&\n        groups[groupId].visibleItemIds.has(itemId) &&\n        groups[groupId].visibleItemIds.size === 1\n      );\n    },\n    addItem(itemId: string, groupId: string) {\n      groups[groupId] ??= {\n        visibleItemIds: new Set<string>(),\n        invisibleItemIds: new Set<string>(),\n      };\n\n      groups[groupId].visibleItemIds.add(itemId);\n      updateGroupVisibility(groupId);\n    },\n    removeItem(itemId: string, groupId: string) {\n      groups[groupId].invisibleItemIds.delete(itemId);\n      groups[groupId].visibleItemIds.delete(itemId);\n      updateGroupVisibility(groupId);\n    },\n    showItem(itemId: string, groupId: string) {\n      groups[groupId].invisibleItemIds.delete(itemId);\n      groups[groupId].visibleItemIds.add(itemId);\n      updateGroupVisibility(groupId);\n    },\n    hideItem(itemId: string, groupId: string) {\n      groups[groupId].invisibleItemIds.add(itemId);\n      groups[groupId].visibleItemIds.delete(itemId);\n      updateGroupVisibility(groupId);\n    },\n  };\n};\n"], "names": ["createOverflowManager", "sizeCache", "Map", "container", "overflowMenu", "observing", "forceDispatch", "options", "padding", "overflowAxis", "overflowDirection", "minimumVisible", "onUpdateItemVisibility", "undefined", "onUpdateOverflow", "overflowItems", "overflowDividers", "disposeResizeObserver", "getNextItem", "queueToDequeue", "queueToEnqueue", "nextItem", "dequeue", "enqueue", "groupManager", "createGroupManager", "compareItems", "lt", "rt", "lte", "rte", "priority", "positionStatusBit", "Node", "DOCUMENT_POSITION_FOLLOWING", "DOCUMENT_POSITION_PRECEDING", "element", "compareDocumentPosition", "getElementAxisSize", "horizontal", "vertical", "el", "has", "set", "get", "getOffsetSize", "bind", "getClientSize", "invisibleItemQueue", "createPriorityQueue", "a", "b", "visibleItemQueue", "occupiedSize", "totalItemSize", "all", "map", "id", "reduce", "prev", "current", "totalDividerSize", "Object", "entries", "groupVisibility", "acc", "state", "overflowMenuSize", "size", "showItem", "item", "visible", "groupId", "isSingleItemVisible", "removeAttribute", "DATA_OVERFLOWING", "hideItem", "setAttribute", "dispatchOverflowUpdate", "visibleItemIds", "invisibleItemIds", "visibleItems", "itemId", "invisibleItems", "processOverflowItems", "clear", "availableSize", "visibleTop", "peek", "invisibleTop", "i", "forceUpdate", "update", "debounce", "observe", "<PERSON><PERSON><PERSON><PERSON>", "userOptions", "assign", "values", "for<PERSON>ach", "observeResize", "addItem", "DATA_OVERFLOW_GROUP", "addOverflowMenu", "addDivider", "divider", "removeOverflowMenu", "removeDivider", "removeItem", "remove", "delete", "disconnect", "keys", "dividerId", "groups", "updateGroupVisibility", "group", "isGroupVisible", "Set", "add"], "rangeMappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "mappings": ";;;;+BAgBgBA;;;eAAAA;;;wBAhBsC;sCACxB;0BACL;+BAC0B;AAa5C,SAASA;IACd,gFAAgF;IAChF,2FAA2F;IAC3F,MAAMC,YAAY,IAAIC;IACtB,IAAIC;IACJ,IAAIC;IACJ,gDAAgD;IAChD,IAAIC,YAAY;IAChB,+FAA+F;IAC/F,kDAAkD;IAClD,IAAIC,gBAAgB;IACpB,MAAMC,UAAoC;QACxCC,SAAS;QACTC,cAAc;QACdC,mBAAmB;QACnBC,gBAAgB;QAChBC,wBAAwB,IAAMC;QAC9BC,kBAAkB,IAAMD;IAC1B;IAEA,MAAME,gBAAmD,CAAC;IAC1D,MAAMC,mBAAyD,CAAC;IAChE,IAAIC,wBAAoC,IAAM;IAE9C,MAAMC,cAAc,CAACC,gBAAuCC;QAC1D,MAAMC,WAAWF,eAAeG,OAAO;QACvCF,eAAeG,OAAO,CAACF;QACvB,OAAON,aAAa,CAACM,SAAS;IAChC;IAEA,MAAMG,eAAeC;IAErB,SAASC,aAAaC,EAAiB,EAAEC,EAAiB;QACxD,IAAI,CAACD,MAAM,CAACC,IAAI;YACd,OAAO;QACT;QAEA,MAAMC,MAAMd,aAAa,CAACY,GAAG;QAC7B,MAAMG,MAAMf,aAAa,CAACa,GAAG;QAE7B,gGAAgG;QAChG,0CAA0C;QAC1C,IAAI,CAACC,OAAO,CAACC,KAAK;YAChB,OAAOD,MAAM,IAAI,CAAC;QACpB;QAEA,IAAIA,IAAIE,QAAQ,KAAKD,IAAIC,QAAQ,EAAE;YACjC,OAAOF,IAAIE,QAAQ,GAAGD,IAAIC,QAAQ,GAAG,IAAI,CAAC;QAC5C;QAEA,MAAMC,oBACJzB,QAAQG,iBAAiB,KAAK,QAAQuB,KAAKC,2BAA2B,GAAGD,KAAKE,2BAA2B;QAE3G,sCAAsC;QACtC,OAAON,IAAIO,OAAO,CAACC,uBAAuB,CAACP,IAAIM,OAAO,IAAIJ,oBAAoB,IAAI,CAAC;IACrF;IAEA,SAASM,mBACPC,UAAyC,EACzCC,QAAyC,EACzCC,EAAe;QAEf,IAAI,CAACxC,UAAUyC,GAAG,CAACD,KAAK;YACtBxC,UAAU0C,GAAG,CAACF,IAAIlC,QAAQE,YAAY,KAAK,eAAegC,EAAE,CAACF,WAAW,GAAGE,EAAE,CAACD,SAAS;QACzF;QAEA,OAAOvC,UAAU2C,GAAG,CAACH;IACvB;IAEA,MAAMI,gBAAgBP,mBAAmBQ,IAAI,CAAC,MAAM,eAAe;IACnE,MAAMC,gBAAgBT,mBAAmBQ,IAAI,CAAC,MAAM,eAAe;IAEnE,MAAME,qBAAqBC,IAAAA,kCAAmB,EAAS,CAACC,GAAGC,IAAM,CAAC,IAAIzB,aAAawB,GAAGC;IAEtF,MAAMC,mBAAmBH,IAAAA,kCAAmB,EAASvB;IAErD,SAAS2B;QACP,MAAMC,gBAAgBF,iBACnBG,GAAG,GACHC,GAAG,CAACC,CAAAA,KAAM1C,aAAa,CAAC0C,GAAG,CAACrB,OAAO,EACnCoB,GAAG,CAACX,eACJa,MAAM,CAAC,CAACC,MAAMC,UAAYD,OAAOC,SAAS;QAE7C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACvC,aAAawC,eAAe,IAAIN,MAAM,CAC5E,CAACO,KAAK,CAACR,IAAIS,MAAM,GACfD,MAAOC,CAAAA,UAAU,YAAYlD,gBAAgB,CAACyC,GAAG,GAAGZ,cAAc7B,gBAAgB,CAACyC,GAAG,CAACrB,OAAO,IAAI,CAAA,GACpG;QAGF,MAAM+B,mBAAmBnB,mBAAmBoB,IAAI,KAAK,KAAKhE,eAAeyC,cAAczC,gBAAgB;QAEvG,OAAOkD,gBAAgBO,mBAAmBM;IAC5C;IAEA,MAAME,WAAW;QACf,MAAMC,OAAOpD,YAAY8B,oBAAoBI;QAC7C7C,QAAQK,sBAAsB,CAAC;YAAE0D;YAAMC,SAAS;QAAK;QAErD,IAAID,KAAKE,OAAO,EAAE;YAChBhD,aAAa6C,QAAQ,CAACC,KAAKb,EAAE,EAAEa,KAAKE,OAAO;YAE3C,IAAIhD,aAAaiD,mBAAmB,CAACH,KAAKb,EAAE,EAAEa,KAAKE,OAAO,GAAG;oBAC3DxD;iBAAAA,iCAAAA,gBAAgB,CAACsD,KAAKE,OAAO,CAAC,cAA9BxD,qDAAAA,+BAAgCoB,OAAO,CAACsC,eAAe,CAACC,wBAAgB;YAC1E;QACF;IACF;IAEA,MAAMC,WAAW;QACf,MAAMN,OAAOpD,YAAYkC,kBAAkBJ;QAC3CzC,QAAQK,sBAAsB,CAAC;YAAE0D;YAAMC,SAAS;QAAM;QAEtD,IAAID,KAAKE,OAAO,EAAE;YAChB,IAAIhD,aAAaiD,mBAAmB,CAACH,KAAKb,EAAE,EAAEa,KAAKE,OAAO,GAAG;oBAC3DxD;iBAAAA,iCAAAA,gBAAgB,CAACsD,KAAKE,OAAO,CAAC,cAA9BxD,qDAAAA,+BAAgCoB,OAAO,CAACyC,YAAY,CAACF,wBAAgB,EAAE;YACzE;YAEAnD,aAAaoD,QAAQ,CAACN,KAAKb,EAAE,EAAEa,KAAKE,OAAO;QAC7C;IACF;IAEA,MAAMM,yBAAyB;QAC7B,MAAMC,iBAAiB3B,iBAAiBG,GAAG;QAC3C,MAAMyB,mBAAmBhC,mBAAmBO,GAAG;QAE/C,MAAM0B,eAAeF,eAAevB,GAAG,CAAC0B,CAAAA,SAAUnE,aAAa,CAACmE,OAAO;QACvE,MAAMC,iBAAiBH,iBAAiBxB,GAAG,CAAC0B,CAAAA,SAAUnE,aAAa,CAACmE,OAAO;QAE3E3E,QAAQO,gBAAgB,CAAC;YAAEmE;YAAcE;YAAgBnB,iBAAiBxC,aAAawC,eAAe;QAAG;IAC3G;IAEA,MAAMoB,uBAAuB;QAC3B,IAAI,CAACjF,WAAW;YACd,OAAO;QACT;QACAF,UAAUoF,KAAK;QAEf,MAAMC,gBAAgBvC,cAAc5C,aAAaI,QAAQC,OAAO;QAEhE,iEAAiE;QACjE,MAAM+E,aAAanC,iBAAiBoC,IAAI;QACxC,MAAMC,eAAezC,mBAAmBwC,IAAI;QAE5C,MAAO9D,aAAasB,mBAAmBwC,IAAI,IAAIpC,iBAAiBoC,IAAI,MAAM,EAAG;YAC3EZ,YAAY,0FAA0F;QACxG;QAEA,wEAAwE;QACxE,wFAAwF;QACxF,IAAK,IAAIc,IAAI,GAAGA,IAAI,GAAGA,IAAK;YAC1B,qEAAqE;YACrE,MACE,AAACrC,iBAAiBiC,iBAAiBtC,mBAAmBoB,IAAI,KAAK,KAC/DpB,mBAAmBoB,IAAI,OAAO,EAAE,8FAA8F;aAC9H;gBACAC;YACF;YAEA,8CAA8C;YAC9C,MAAOhB,iBAAiBiC,iBAAiBlC,iBAAiBgB,IAAI,KAAK7D,QAAQI,cAAc,CAAE;gBACzFiE;YACF;QACF;QAEA,oEAAoE;QACpE,OAAOxB,iBAAiBoC,IAAI,OAAOD,cAAcvC,mBAAmBwC,IAAI,OAAOC;IACjF;IAEA,MAAME,cAA8C;QAClD,IAAIP,0BAA0B9E,eAAe;YAC3CA,gBAAgB;YAChBwE;QACF;IACF;IAEA,MAAMc,SAAoCC,IAAAA,kBAAQ,EAACF;IAEnD,MAAMG,UAAsC,CAACC,mBAAmBC;QAC9DlC,OAAOmC,MAAM,CAAC1F,SAASyF;QACvB3F,YAAY;QACZyD,OAAOoC,MAAM,CAACnF,eAAeoF,OAAO,CAAC7B,CAAAA,OAAQlB,iBAAiB7B,OAAO,CAAC+C,KAAKb,EAAE;QAE7EtD,YAAY4F;QACZ9E,wBAAwBmF,IAAAA,mCAAa,EAACjG,WAAW4D,CAAAA;YAC/C,IAAI,CAACA,OAAO,CAAC,EAAE,IAAI,CAAC5D,WAAW;gBAC7B;YACF;YAEAyF;QACF;IACF;IAEA,MAAMS,UAAsC/B,CAAAA;QAC1C,IAAIvD,aAAa,CAACuD,KAAKb,EAAE,CAAC,EAAE;YAC1B;QACF;QAEA1C,aAAa,CAACuD,KAAKb,EAAE,CAAC,GAAGa;QAEzB,mEAAmE;QACnE,IAAIjE,WAAW;YACb,sDAAsD;YACtD,uEAAuE;YACvE,8CAA8C;YAC9CC,gBAAgB;YAChB8C,iBAAiB7B,OAAO,CAAC+C,KAAKb,EAAE;QAClC;QAEA,IAAIa,KAAKE,OAAO,EAAE;YAChBhD,aAAa6E,OAAO,CAAC/B,KAAKb,EAAE,EAAEa,KAAKE,OAAO;YAC1CF,KAAKlC,OAAO,CAACyC,YAAY,CAACyB,2BAAmB,EAAEhC,KAAKE,OAAO;QAC7D;QAEAoB;IACF;IAEA,MAAMW,kBAAsD9D,CAAAA;QAC1DrC,eAAeqC;IACjB;IAEA,MAAM+D,aAA4CC,CAAAA;QAChD,IAAI,CAACA,QAAQjC,OAAO,IAAIxD,gBAAgB,CAACyF,QAAQjC,OAAO,CAAC,EAAE;YACzD;QACF;QAEAiC,QAAQrE,OAAO,CAACyC,YAAY,CAACyB,2BAAmB,EAAEG,QAAQjC,OAAO;QACjExD,gBAAgB,CAACyF,QAAQjC,OAAO,CAAC,GAAGiC;IACtC;IAEA,MAAMC,qBAA4D;QAChEtG,eAAeS;IACjB;IAEA,MAAM8F,gBAAkDnC,CAAAA;QACtD,IAAI,CAACxD,gBAAgB,CAACwD,QAAQ,EAAE;YAC9B;QACF;QACA,MAAMiC,UAAUzF,gBAAgB,CAACwD,QAAQ;QACzC,IAAIiC,QAAQjC,OAAO,EAAE;YACnB,OAAOxD,gBAAgB,CAACwD,QAAQ;YAChCiC,QAAQrE,OAAO,CAACsC,eAAe,CAAC4B,2BAAmB;QACrD;IACF;IAEA,MAAMM,aAA4C1B,CAAAA;QAChD,IAAI,CAACnE,aAAa,CAACmE,OAAO,EAAE;YAC1B;QACF;QAEA,IAAI7E,WAAW;YACb,+EAA+E;YAC/E,iEAAiE;YACjEC,gBAAgB;QAClB;QAEA,MAAMgE,OAAOvD,aAAa,CAACmE,OAAO;QAClC9B,iBAAiByD,MAAM,CAAC3B;QACxBlC,mBAAmB6D,MAAM,CAAC3B;QAE1B,IAAIZ,KAAKE,OAAO,EAAE;YAChBhD,aAAaoF,UAAU,CAACtC,KAAKb,EAAE,EAAEa,KAAKE,OAAO;YAC7CF,KAAKlC,OAAO,CAACsC,eAAe,CAAC4B,2BAAmB;QAClD;QAEArG,UAAU6G,MAAM,CAACxC,KAAKlC,OAAO;QAC7B,OAAOrB,aAAa,CAACmE,OAAO;QAC5BU;IACF;IAEA,MAAMmB,aAA4C;QAChD9F;QAEA,cAAc;QACdd,YAAYU;QACZR,YAAY;QACZC,gBAAgB;QAEhB,oBAAoB;QACpBwD,OAAOkD,IAAI,CAACjG,eAAeoF,OAAO,CAACjB,CAAAA,SAAU0B,WAAW1B;QACxDpB,OAAOkD,IAAI,CAAChG,kBAAkBmF,OAAO,CAACc,CAAAA,YAAaN,cAAcM;QACjEP;QACAzG,UAAUoF,KAAK;IACjB;IAEA,OAAO;QACLgB;QACAU;QACApB;QACAG;QACAc;QACAhB;QACAW;QACAG;QACAF;QACAG;IACF;AACF;AAEA,MAAMlF,qBAAqB;IACzB,MAAMuC,kBAAsD,CAAC;IAC7D,MAAMkD,SAAyF,CAAC;IAChG,SAASC,sBAAsB3C,OAAe;QAC5C,MAAM4C,QAAQF,MAAM,CAAC1C,QAAQ;QAC7B,IAAI4C,MAAMpC,gBAAgB,CAACZ,IAAI,IAAIgD,MAAMrC,cAAc,CAACX,IAAI,EAAE;YAC5DJ,eAAe,CAACQ,QAAQ,GAAG;QAC7B,OAAO,IAAI4C,MAAMrC,cAAc,CAACX,IAAI,KAAK,GAAG;YAC1CJ,eAAe,CAACQ,QAAQ,GAAG;QAC7B,OAAO;YACLR,eAAe,CAACQ,QAAQ,GAAG;QAC7B;IACF;IACA,SAAS6C,eAAe7C,OAAe;QACrC,OAAOR,eAAe,CAACQ,QAAQ,KAAK,aAAaR,eAAe,CAACQ,QAAQ,KAAK;IAChF;IACA,OAAO;QACLR,iBAAiB,IAAMA;QACvBS,qBAAoBS,MAAc,EAAEV,OAAe;YACjD,OACE6C,eAAe7C,YACf0C,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAACrC,GAAG,CAACwC,WACnCgC,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAACX,IAAI,KAAK;QAE5C;QACAiC,SAAQnB,MAAc,EAAEV,OAAe;gBACrC0C,SAAO1C;;YAAP0C,MAAAA,UAAAA,OAAM,CAAC1C,WAAAA,QAAQ,iCAAf0C,OAAM,CAAC1C,SAAQ,GAAK;gBAClBO,gBAAgB,IAAIuC;gBACpBtC,kBAAkB,IAAIsC;YACxB;YAEAJ,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAACwC,GAAG,CAACrC;YACnCiC,sBAAsB3C;QACxB;QACAoC,YAAW1B,MAAc,EAAEV,OAAe;YACxC0C,MAAM,CAAC1C,QAAQ,CAACQ,gBAAgB,CAAC8B,MAAM,CAAC5B;YACxCgC,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAAC+B,MAAM,CAAC5B;YACtCiC,sBAAsB3C;QACxB;QACAH,UAASa,MAAc,EAAEV,OAAe;YACtC0C,MAAM,CAAC1C,QAAQ,CAACQ,gBAAgB,CAAC8B,MAAM,CAAC5B;YACxCgC,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAACwC,GAAG,CAACrC;YACnCiC,sBAAsB3C;QACxB;QACAI,UAASM,MAAc,EAAEV,OAAe;YACtC0C,MAAM,CAAC1C,QAAQ,CAACQ,gBAAgB,CAACuC,GAAG,CAACrC;YACrCgC,MAAM,CAAC1C,QAAQ,CAACO,cAAc,CAAC+B,MAAM,CAAC5B;YACtCiC,sBAAsB3C;QACxB;IACF;AACF"}