/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { TabsterPart } from "./Utils";
export declare abstract class DeloserItemBase<C> {
    abstract resetFocus(): Promise<boolean>;
    abstract belongsTo(deloser: C): boolean;
}
export declare class DeloserItem extends DeloserItemBase<Types.Deloser> {
    readonly uid: string;
    private _tabster;
    private _deloser;
    constructor(tabster: Types.TabsterCore, deloser: Types.Deloser);
    belongsTo(deloser: Types.Deloser): boolean;
    unshift(element: HTMLElement): void;
    focusAvailable(): Promise<boolean | null>;
    resetFocus(): Promise<boolean>;
}
export declare abstract class DeloserHistoryByRootBase<I, D extends DeloserItemBase<I>> {
    protected _tabster: Types.TabsterCore;
    protected _history: D[];
    readonly rootUId: string;
    constructor(tabster: Types.TabsterCore, rootUId: string);
    getLength(): number;
    removeDeloser(deloser: I): void;
    hasDeloser(deloser: I): boolean;
    abstract focusAvailable(from: I | null): Promise<boolean | null>;
    abstract resetFocus(from: I | null): Promise<boolean>;
}
export declare class DeloserHistory {
    private _tabster;
    private _history;
    constructor(tabster: Types.TabsterCore);
    dispose(): void;
    process(element: HTMLElement): Types.Deloser | undefined;
    make<I, D extends DeloserItemBase<I>, C extends DeloserHistoryByRootBase<I, D>>(rootUId: string, createInstance: () => C): C;
    removeDeloser(deloser: Types.Deloser): void;
    focusAvailable(from: Types.Deloser | null): Promise<boolean | null>;
    resetFocus(from: Types.Deloser | null): Promise<boolean>;
}
export declare class Deloser extends TabsterPart<Types.DeloserProps> implements Types.Deloser {
    readonly uid: string;
    readonly strategy: Types.DeloserStrategy;
    private _isActive;
    private _history;
    private _snapshotIndex;
    private _onDispose;
    constructor(tabster: Types.TabsterCore, element: HTMLElement, onDispose: (deloser: Deloser) => void, props: Types.DeloserProps);
    dispose(): void;
    isActive: () => boolean;
    setActive(active: boolean): void;
    getActions(): Types.DeloserElementActions;
    setSnapshot: (index: number) => void;
    focusFirst: () => boolean;
    unshift(element: HTMLElement): void;
    focusDefault: () => boolean;
    resetFocus: () => boolean;
    findAvailable(): HTMLElement | null;
    clearHistory: (preserveExisting?: boolean) => void;
    customFocusLostHandler(element: HTMLElement): boolean;
    private _findInHistory;
    private _findFirst;
    private _remove;
}
export declare class DeloserAPI implements Types.DeloserAPI {
    private _tabster;
    private _win;
    /**
     * Tracks if focus is inside a deloser
     */
    private _inDeloser;
    private _curDeloser;
    private _history;
    private _restoreFocusTimer;
    private _isRestoringFocus;
    private _isPaused;
    private _autoDeloser;
    private _autoDeloserInstance;
    constructor(tabster: Types.TabsterCore, props?: {
        autoDeloser: Types.DeloserProps;
    });
    dispose(): void;
    createDeloser(element: HTMLElement, props: Types.DeloserProps): Types.Deloser;
    getActions(element: HTMLElement): Types.DeloserElementActions | undefined;
    pause(): void;
    resume(restore?: boolean): void;
    private _onRestoreFocus;
    private _onFocus;
    /**
     * Activates and sets the current deloser
     */
    private _activate;
    /**
     * Called when focus should no longer be in a deloser
     */
    private _deactivate;
    private _scheduleRestoreFocus;
    static getDeloser(tabster: Types.TabsterCore, element: HTMLElement): Types.Deloser | undefined;
    private _onDeloserDispose;
    static getHistory(instance: Types.DeloserAPI): DeloserHistory;
    static forceRestoreFocus(instance: Types.DeloserAPI): void;
}
