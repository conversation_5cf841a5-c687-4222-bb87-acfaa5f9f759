{"version": 3, "file": "azureKeyCredential.js", "sourceRoot": "", "sources": ["../../src/azureKeyCredential.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAIlC;;;GAGG;AACH,MAAa,kBAAkB;IAG7B;;OAEG;IACH,IAAW,GAAG;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,YAAY,GAAW;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,MAAc;QAC1B,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;IACrB,CAAC;CACF;AAnCD,gDAmCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { KeyCredential } from \"./keyCredential.js\";\n\n/**\n * A static-key-based credential that supports updating\n * the underlying key value.\n */\nexport class AzureKeyCredential implements KeyCredential {\n  private _key: string;\n\n  /**\n   * The value of the key to be used in authentication\n   */\n  public get key(): string {\n    return this._key;\n  }\n\n  /**\n   * Create an instance of an AzureKeyCredential for use\n   * with a service client.\n   *\n   * @param key - The initial value of the key to use in authentication\n   */\n  constructor(key: string) {\n    if (!key) {\n      throw new Error(\"key must be a non-empty string\");\n    }\n\n    this._key = key;\n  }\n\n  /**\n   * Change the value of the key.\n   *\n   * Updates will take effect upon the next request after\n   * updating the key value.\n   *\n   * @param newKey - The new key value to be used\n   */\n  public update(newKey: string): void {\n    this._key = newKey;\n  }\n}\n"]}