{"version": 3, "file": "tracingClient.js", "sourceRoot": "", "sources": ["../../src/tracingClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAYlC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEvD;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAA6B;IAC/D,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;IAE3D,SAAS,SAAS,CAChB,IAAY,EACZ,gBAA0B,EAC1B,WAAgC;;QAKhC,MAAM,eAAe,GAAG,eAAe,EAAE,CAAC,SAAS,CAAC,IAAI,kCACnD,WAAW,KACd,WAAW,EAAE,WAAW,EACxB,cAAc,EAAE,cAAc,EAC9B,cAAc,EAAE,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,cAAc,0CAAE,cAAc,IAChE,CAAC;QACH,IAAI,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;QACpD,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;YACzD,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAClF,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;QACvF,MAAM,cAAc,GAAuC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE;YAC7F,cAAc,kCAAO,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,cAAc,KAAE,cAAc,GAAE;SACxE,CAAC,CAAC;QAEH,OAAO;YACL,IAAI;YACJ,cAAc;SACf,CAAC;IACJ,CAAC;IAED,KAAK,UAAU,QAAQ,CAOrB,IAAY,EACZ,gBAAyB,EACzB,QAAkB,EAClB,WAAgC;QAEhC,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAChF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,GAAG,EAAE,CAClF,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAChD,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;YACtC,OAAO,MAAqC,CAAC;QAC/C,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAChD,MAAM,GAAG,CAAC;QACZ,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,CAAC;IACH,CAAC;IAED,SAAS,WAAW,CAIlB,OAAuB,EACvB,QAAkB,EAClB,GAAG,YAA0B;QAE7B,OAAO,eAAe,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACH,SAAS,sBAAsB,CAAC,iBAAyB;QACvD,OAAO,eAAe,EAAE,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACH,SAAS,oBAAoB,CAAC,cAA+B;QAC3D,OAAO,eAAe,EAAE,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;IAChE,CAAC;IAED,OAAO;QACL,SAAS;QACT,QAAQ;QACR,WAAW;QACX,sBAAsB;QACtB,oBAAoB;KACrB,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  OperationTracingOptions,\n  OptionsWithTracingContext,\n  Resolved,\n  TracingClient,\n  TracingClientOptions,\n  TracingContext,\n  TracingSpan,\n  TracingSpanOptions,\n} from \"./interfaces.js\";\nimport { getInstrumenter } from \"./instrumenter.js\";\nimport { knownContextKeys } from \"./tracingContext.js\";\n\n/**\n * Creates a new tracing client.\n *\n * @param options - Options used to configure the tracing client.\n * @returns - An instance of {@link TracingClient}.\n */\nexport function createTracingClient(options: TracingClientOptions): TracingClient {\n  const { namespace, packageName, packageVersion } = options;\n\n  function startSpan<Options extends { tracingOptions?: OperationTracingOptions }>(\n    name: string,\n    operationOptions?: Options,\n    spanOptions?: TracingSpanOptions,\n  ): {\n    span: TracingSpan;\n    updatedOptions: OptionsWithTracingContext<Options>;\n  } {\n    const startSpanResult = getInstrumenter().startSpan(name, {\n      ...spanOptions,\n      packageName: packageName,\n      packageVersion: packageVersion,\n      tracingContext: operationOptions?.tracingOptions?.tracingContext,\n    });\n    let tracingContext = startSpanResult.tracingContext;\n    const span = startSpanResult.span;\n    if (!tracingContext.getValue(knownContextKeys.namespace)) {\n      tracingContext = tracingContext.setValue(knownContextKeys.namespace, namespace);\n    }\n    span.setAttribute(\"az.namespace\", tracingContext.getValue(knownContextKeys.namespace));\n    const updatedOptions: OptionsWithTracingContext<Options> = Object.assign({}, operationOptions, {\n      tracingOptions: { ...operationOptions?.tracingOptions, tracingContext },\n    });\n\n    return {\n      span,\n      updatedOptions,\n    };\n  }\n\n  async function withSpan<\n    Options extends { tracingOptions?: OperationTracingOptions },\n    Callback extends (\n      updatedOptions: Options,\n      span: Omit<TracingSpan, \"end\">,\n    ) => ReturnType<Callback>,\n  >(\n    name: string,\n    operationOptions: Options,\n    callback: Callback,\n    spanOptions?: TracingSpanOptions,\n  ): Promise<Resolved<ReturnType<Callback>>> {\n    const { span, updatedOptions } = startSpan(name, operationOptions, spanOptions);\n    try {\n      const result = await withContext(updatedOptions.tracingOptions.tracingContext, () =>\n        Promise.resolve(callback(updatedOptions, span)),\n      );\n      span.setStatus({ status: \"success\" });\n      return result as ReturnType<typeof withSpan>;\n    } catch (err: any) {\n      span.setStatus({ status: \"error\", error: err });\n      throw err;\n    } finally {\n      span.end();\n    }\n  }\n\n  function withContext<\n    CallbackArgs extends unknown[],\n    Callback extends (...args: CallbackArgs) => ReturnType<Callback>,\n  >(\n    context: TracingContext,\n    callback: Callback,\n    ...callbackArgs: CallbackArgs\n  ): ReturnType<Callback> {\n    return getInstrumenter().withContext(context, callback, ...callbackArgs);\n  }\n\n  /**\n   * Parses a traceparent header value into a span identifier.\n   *\n   * @param traceparentHeader - The traceparent header to parse.\n   * @returns An implementation-specific identifier for the span.\n   */\n  function parseTraceparentHeader(traceparentHeader: string): TracingContext | undefined {\n    return getInstrumenter().parseTraceparentHeader(traceparentHeader);\n  }\n\n  /**\n   * Creates a set of request headers to propagate tracing information to a backend.\n   *\n   * @param tracingContext - The context containing the span to serialize.\n   * @returns The set of headers to add to a request.\n   */\n  function createRequestHeaders(tracingContext?: TracingContext): Record<string, string> {\n    return getInstrumenter().createRequestHeaders(tracingContext);\n  }\n\n  return {\n    startSpan,\n    withSpan,\n    withContext,\n    parseTraceparentHeader,\n    createRequestHeaders,\n  };\n}\n"]}