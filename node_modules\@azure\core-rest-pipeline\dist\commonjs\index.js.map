{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAkClC,6CAMuB;AADrB,kHAAA,mBAAmB,OAAA;AAErB,+EAKwC;AAJtC,yIAAA,yBAAyB,OAAA;AAK3B,+DAAiE;AAAxD,+HAAA,uBAAuB,OAAA;AAChC,mDAAqD;AAA5C,mHAAA,iBAAiB,OAAA;AAC1B,2DAA0F;AAAjF,2HAAA,qBAAqB,OAAA;AAC9B,+CAA+E;AAAtE,yGAAA,SAAS,OAAA;AAAyB,2GAAA,WAAW,OAAA;AACtD,sFAGgD;AAF9C,uIAAA,wBAAwB,OAAA;AACxB,2IAAA,4BAA4B,OAAA;AAE9B,kFAI8C;AAH5C,mIAAA,sBAAsB,OAAA;AAEtB,uIAAA,0BAA0B,OAAA;AAE5B,sFAGgD;AAF9C,uIAAA,wBAAwB,OAAA;AACxB,2IAAA,4BAA4B,OAAA;AAE9B,wDAA0F;AAAjF,yGAAA,SAAS,OAAA;AAAE,6GAAA,aAAa,OAAA;AACjC,oEAAqF;AAA5E,qHAAA,eAAe,OAAA;AAAE,yHAAA,mBAAmB,OAAA;AAC7C,4DAAkG;AAAzF,6GAAA,WAAW,OAAA;AAAE,iHAAA,eAAe,OAAA;AAAE,yHAAA,uBAAuB,OAAA;AAC9D,kEAIsC;AAHpC,mHAAA,cAAc,OAAA;AACd,uHAAA,kBAAkB,OAAA;AAGpB,kFAI8C;AAH5C,mIAAA,sBAAsB,OAAA;AAEtB,uIAAA,0BAA0B,OAAA;AAE5B,gFAI6C;AAH3C,iIAAA,qBAAqB,OAAA;AACrB,qIAAA,yBAAyB,OAAA;AAG3B,4DAAiF;AAAxE,6GAAA,WAAW,OAAA;AAMpB,gEAIqC;AAHnC,iHAAA,aAAa,OAAA;AACb,qHAAA,iBAAiB,OAAA;AAGnB,0EAG0C;AAFxC,2HAAA,kBAAkB,OAAA;AAGpB,oEAIuC;AAHrC,qHAAA,eAAe,OAAA;AACf,yHAAA,mBAAmB,OAAA;AAGrB,wDAAmE;AAA1D,yGAAA,SAAS,OAAA;AAAE,6GAAA,aAAa,OAAA;AACjC,kEAAkF;AAAzE,mHAAA,cAAc,OAAA;AAAE,uHAAA,kBAAkB,OAAA;AAC3C,oGAOuD;AANrD,qJAAA,+BAA+B,OAAA;AAE/B,yJAAA,mCAAmC,OAAA;AAKrC,8DAA4E;AAAnE,+GAAA,YAAY,OAAA;AAAE,mHAAA,gBAAgB,OAAA;AACvC,4GAI2D;AAHzD,6JAAA,mCAAmC,OAAA;AAEnC,iKAAA,uCAAuC,OAAA;AAEzC,0CAKwB;AAJtB,qGAAA,UAAU,OAAA;AACV,+GAAA,oBAAoB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\ndeclare global {\n  interface FormData {}\n  interface Blob {}\n  interface File {}\n  interface ReadableStream<R = any> {}\n  interface TransformStream<I = any, O = any> {}\n}\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nexport type {\n  Agent,\n  BodyPart,\n  FormDataMap,\n  FormDataValue,\n  HttpClient,\n  HttpHeaders,\n  HttpMethods,\n  KeyObject,\n  MultipartRequestBody,\n  PipelineRequest,\n  PipelineResponse,\n  PipelineRetryOptions,\n  ProxySettings,\n  PxfObject,\n  RawHttpHeaders,\n  RawHttpHeadersInput,\n  RequestBodyType,\n  SendRequest,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nexport {\n  type AddPolicyOptions as AddPipelineOptions,\n  type PipelinePhase,\n  type PipelinePolicy,\n  type Pipeline,\n  createEmptyPipeline,\n} from \"./pipeline.js\";\nexport {\n  createPipelineFromOptions,\n  type TelemetryOptions,\n  type InternalPipelineOptions,\n  type PipelineOptions,\n} from \"./createPipelineFromOptions.js\";\nexport { createDefaultHttpClient } from \"./defaultHttpClient.js\";\nexport { createHttpHeaders } from \"./httpHeaders.js\";\nexport { createPipelineRequest, type PipelineRequestOptions } from \"./pipelineRequest.js\";\nexport { RestError, type RestErrorOptions, isRestError } from \"./restError.js\";\nexport {\n  decompressResponsePolicy,\n  decompressResponsePolicyName,\n} from \"./policies/decompressResponsePolicy.js\";\nexport {\n  exponentialRetryPolicy,\n  type ExponentialRetryPolicyOptions,\n  exponentialRetryPolicyName,\n} from \"./policies/exponentialRetryPolicy.js\";\nexport {\n  setClientRequestIdPolicy,\n  setClientRequestIdPolicyName,\n} from \"./policies/setClientRequestIdPolicy.js\";\nexport { logPolicy, logPolicyName, type LogPolicyOptions } from \"./policies/logPolicy.js\";\nexport { multipartPolicy, multipartPolicyName } from \"./policies/multipartPolicy.js\";\nexport { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from \"./policies/proxyPolicy.js\";\nexport {\n  redirectPolicy,\n  redirectPolicyName,\n  type RedirectPolicyOptions,\n} from \"./policies/redirectPolicy.js\";\nexport {\n  systemErrorRetryPolicy,\n  type SystemErrorRetryPolicyOptions,\n  systemErrorRetryPolicyName,\n} from \"./policies/systemErrorRetryPolicy.js\";\nexport {\n  throttlingRetryPolicy,\n  throttlingRetryPolicyName,\n  type ThrottlingRetryPolicyOptions,\n} from \"./policies/throttlingRetryPolicy.js\";\nexport { retryPolicy, type RetryPolicyOptions } from \"./policies/retryPolicy.js\";\nexport type {\n  RetryStrategy,\n  RetryInformation,\n  RetryModifiers,\n} from \"./retryStrategies/retryStrategy.js\";\nexport {\n  tracingPolicy,\n  tracingPolicyName,\n  type TracingPolicyOptions,\n} from \"./policies/tracingPolicy.js\";\nexport {\n  defaultRetryPolicy,\n  type DefaultRetryPolicyOptions,\n} from \"./policies/defaultRetryPolicy.js\";\nexport {\n  userAgentPolicy,\n  userAgentPolicyName,\n  type UserAgentPolicyOptions,\n} from \"./policies/userAgentPolicy.js\";\nexport { tlsPolicy, tlsPolicyName } from \"./policies/tlsPolicy.js\";\nexport { formDataPolicy, formDataPolicyName } from \"./policies/formDataPolicy.js\";\nexport {\n  bearerTokenAuthenticationPolicy,\n  type BearerTokenAuthenticationPolicyOptions,\n  bearerTokenAuthenticationPolicyName,\n  type ChallengeCallbacks,\n  type AuthorizeRequestOptions,\n  type AuthorizeRequestOnChallengeOptions,\n} from \"./policies/bearerTokenAuthenticationPolicy.js\";\nexport { ndJsonPolicy, ndJsonPolicyName } from \"./policies/ndJsonPolicy.js\";\nexport {\n  auxiliaryAuthenticationHeaderPolicy,\n  type AuxiliaryAuthenticationHeaderPolicyOptions,\n  auxiliaryAuthenticationHeaderPolicyName,\n} from \"./policies/auxiliaryAuthenticationHeaderPolicy.js\";\nexport {\n  createFile,\n  createFileFromStream,\n  type CreateFileOptions,\n  type CreateFileFromStreamOptions,\n} from \"./util/file.js\";\n"]}