/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { HTMLElementWithUID } from "./Utils";
export declare function observeMutations(doc: Document, tabster: Types.TabsterCore, updateTabsterByAttribute: (tabster: Types.TabsterCore, element: HTMLElementWithUID, dispose?: boolean) => void, syncState: boolean): () => void;
