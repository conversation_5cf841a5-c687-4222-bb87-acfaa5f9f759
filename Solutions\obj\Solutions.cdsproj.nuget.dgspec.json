{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\PCF\\PropertyHiearchy\\Solutions\\Solutions.cdsproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\PCF\\PropertyHiearchy\\Solutions\\Solutions.cdsproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\PCF\\PropertyHiearchy\\Solutions\\Solutions.cdsproj", "projectName": "Solutions", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\PCF\\PropertyHiearchy\\Solutions\\Solutions.cdsproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\PCF\\PropertyHiearchy\\Solutions\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Downloads": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}}}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "Microsoft.PowerApps.MSBuild.Solution": {"target": "Package", "version": "[1.*, )"}}}}}}}