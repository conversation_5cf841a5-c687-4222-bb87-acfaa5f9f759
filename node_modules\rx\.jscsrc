{"excludeFiles": ["src/core/asyncintro.js", "src/core/intro.js", "src/core/outro.js", "src/core/suboutro.js", "src/core/subintro.js", "src/core/testintro.js"], "requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "disallowMultipleLineStrings": true, "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowSpaceAfterPrefixUnaryOperators": true, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireLineFeedAtFileEnd": true, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInsideParentheses": true, "disallowMultipleLineBreaks": true, "disallowNewlineBeforeBlockStatements": true}