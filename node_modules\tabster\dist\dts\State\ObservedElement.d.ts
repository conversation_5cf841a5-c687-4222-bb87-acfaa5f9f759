/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "../Types";
import { Subscribable } from "./Subscribable";
export declare class ObservedElementAPI extends Subscribable<HTMLElement, Types.ObservedElementDetails> implements Types.ObservedElementAPI {
    private _win;
    private _tabster;
    private _waiting;
    private _lastRequestFocusId;
    private _observedById;
    private _observedByName;
    private _currentRequest;
    private _currentRequestTimestamp;
    constructor(tabster: Types.TabsterCore);
    dispose(): void;
    private _onFocus;
    private _rejectWaiting;
    private _isObservedNamesUpdated;
    /**
     * Returns existing element by observed name
     *
     * @param observedName An observed name
     * @param accessibility Optionally, return only if the element is accessible or focusable
     * @returns HTMLElement | null
     */
    getElement(observedName: string, accessibility?: Types.ObservedElementAccessibility): HTMLElement | null;
    /**
     * Waits for the element to appear in the DOM and returns it.
     *
     * @param observedName An observed name
     * @param timeout Wait no longer than this timeout
     * @param accessibility Optionally, wait for the element to also become accessible or focusable before returning it
     * @returns Promise<HTMLElement | null>
     */
    waitElement(observedName: string, timeout: number, accessibility?: Types.ObservedElementAccessibility): Types.ObservedElementAsyncRequest<HTMLElement | null>;
    requestFocus(observedName: string, timeout: number, options?: Pick<FocusOptions, "preventScroll">): Types.ObservedElementAsyncRequest<boolean>;
    onObservedElementUpdate: (element: HTMLElement) => void;
    private _waitConditional;
}
