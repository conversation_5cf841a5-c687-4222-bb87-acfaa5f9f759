/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
export declare function getActiveElement(doc: Document): Element | null;
export declare function nodeContains(node: Node | null | undefined, otherNode: Node | null | undefined): boolean;
export declare function getParentNode(node: Node | null | undefined): ParentNode | null;
export declare function getParentElement(element: HTMLElement | null | undefined): HTMLElement | null;
export declare function getFirstChild(node: Node | null | undefined): ChildNode | null;
export declare function getLastChild(node: Node | null | undefined): ChildNode | null;
export declare function getNextSibling(node: Node | null | undefined): ChildNode | null;
export declare function getPreviousSibling(node: Node | null | undefined): ChildNode | null;
export declare function getFirstElementChild(element: Element | null | undefined): Element | null;
export declare function getLastElementChild(element: Element | null | undefined): Element | null;
export declare function getNextElementSibling(element: Element | null | undefined): Element | null;
export declare function getPreviousElementSibling(element: Element | null | undefined): Element | null;
export declare function appendChild(parent: Node, child: Node): Node;
export declare function insertBefore(parent: Node, child: Node, referenceChild: Node | null): Node;
export declare function getSelection(ref: Node): Selection | null;
export declare function getElementsByName(referenceElement: HTMLElement, name: string): NodeListOf<HTMLElement>;
