/**
 * CRM Data Service for fetching property data using FetchXML
 * Handles communication with Dynamics 365 CRM to retrieve property hierarchy data
 */

import { Property } from '../types/DiagramTypes';

/**
 * Interface for CRM property entity data
 */
interface CrmPropertyEntity {
    cff_propertyid: string;
    cff_name: string;
    cff_propertytype: string | number;
    cff_propertyuse: string | number;
    cff_parentpropertyid?: string;
    cff_mainpropertyid?: string;
    cff_grossleasableareagla?: number;
    createdon: string;
}

/**
 * CRM Data Service class for handling property data operations
 */
export class CrmDataService {
    private context: ComponentFramework.Context<unknown>;
    private entityId: string;

    constructor(context: ComponentFramework.Context<unknown>, entityId: string) {
        this.context = context;
        this.entityId = entityId;
    }

    /**
     * Fetches the main property for the current entity record
     * @returns Promise<CrmPropertyEntity | null>
     */
    private async fetchMainProperty(): Promise<CrmPropertyEntity | null> {
        console.log('🔍 Fetching main property for entity ID:', this.entityId);

        const fetchXml = `
            <fetch version="1.0" output-format="xml-platform" mapping="logical" distinct="false">
                <entity name="cff_property">
                    <attribute name="cff_propertyid"/>
                    <attribute name="cff_name"/>
                    <attribute name="createdon"/>
                    <attribute name="cff_propertyuse"/>
                    <attribute name="cff_propertytype"/>
                    <attribute name="cff_parentpropertyid"/>
                    <attribute name="cff_mainpropertyid"/>
                    <attribute name="cff_grossleasableareagla"/>
                    <order attribute="cff_name" descending="false"/>
                    <filter type="and">
                        <condition attribute="cff_propertyid" operator="eq" value="${this.entityId}"/> 
                    </filter>
                </entity>
            </fetch>
        `;

        try {
            const result = await this.context.webAPI.retrieveMultipleRecords(
                'cff_property',
                `?fetchXml=${encodeURIComponent(fetchXml)}`
            );

            if (result.entities && result.entities.length > 0) {
                const mainProperty = result.entities[0] as CrmPropertyEntity;
                console.log('✅ Main property found:', mainProperty);
                return mainProperty;
            } else {
                console.log('⚠️ No main property found for entity ID:', this.entityId);
                return null;
            }
        } catch (error) {
            console.error('❌ Error fetching main property:', error);
            throw error;
        }
    }

    /**
     * Fetches all properties connected to the main property
     * @param mainPropertyId The ID of the main property
     * @returns Promise<CrmPropertyEntity[]>
     */
    private async fetchConnectedProperties(mainPropertyId: string): Promise<CrmPropertyEntity[]> {
        console.log('🔍 Fetching connected properties for main property ID:', mainPropertyId);

        const fetchXml = `
            <fetch version="1.0" output-format="xml-platform" mapping="logical" distinct="false">
                <entity name="cff_property">
                    <attribute name="cff_propertyid"/>
                    <attribute name="cff_name"/>
                    <attribute name="createdon"/>
                    <attribute name="cff_propertyuse"/>
                    <attribute name="cff_propertytype"/>
                    <attribute name="cff_parentpropertyid"/>
                    <attribute name="cff_mainpropertyid"/>
                    <attribute name="cff_grossleasableareagla"/>
                    <order attribute="cff_name" descending="false"/>
                    <filter type="and">
                        <condition attribute="cff_mainpropertyid" operator="eq" value="${mainPropertyId}"/> 
                    </filter>
                </entity>
            </fetch>
        `;

        try {
            const result = await this.context.webAPI.retrieveMultipleRecords(
                'cff_property',
                `?fetchXml=${encodeURIComponent(fetchXml)}`
            );

            const connectedProperties = result.entities as CrmPropertyEntity[];
            console.log(`✅ Found ${connectedProperties.length} connected properties`);
            return connectedProperties;
        } catch (error) {
            console.error('❌ Error fetching connected properties:', error);
            throw error;
        }
    }

    /**
     * Converts CRM property entity to Property interface
     * @param crmProperty CRM property entity
     * @returns Property object
     */
    private convertCrmPropertyToProperty(crmProperty: CrmPropertyEntity): Property {
        return {
            id: crmProperty.cff_propertyid,
            name: crmProperty.cff_name || 'Unnamed Property',
            type: this.getPropertyTypeString(crmProperty.cff_propertytype),
            parentPropertyId: crmProperty.cff_parentpropertyid || undefined,
            isMainProperty: !crmProperty.cff_parentpropertyid, // Main property has no parent
            metadata: {
                propertyUse: this.getPropertyUseString(crmProperty.cff_propertyuse),
                grossLeasableArea: crmProperty.cff_grossleasableareagla || 0,
                createdOn: crmProperty.createdon,
                mainPropertyId: crmProperty.cff_mainpropertyid
            }
        };
    }

    /**
     * Converts property type number to string
     * @param propertyType Property type (number or string)
     * @returns Property type string
     */
    private getPropertyTypeString(propertyType: string | number): string {
        if (typeof propertyType === 'string') {
            return propertyType;
        }

        // Convert numeric property type to string
        switch (propertyType) {
            case 100000000: return 'Park';
            case 100000001: return 'Landplot';
            case 100000002: return 'Building';
            case 100000003: return 'Section';
            case 100000004: return 'Floor';
            case 100000005: return 'Unit';
            default: return 'Unknown';
        }
    }

    /**
     * Converts property use number to string
     * @param propertyUse Property use (number or string)
     * @returns Property use string
     */
    private getPropertyUseString(propertyUse: string | number): string {
        if (typeof propertyUse === 'string') {
            return propertyUse;
        }

        // Convert numeric property use to string (add mappings as needed)
        switch (propertyUse) {
            case 100000000: return 'Industrial';
            case 100000001: return 'Business';
            case 100000002: return 'Residential';
            case 100000003: return 'Commercial';
            case 100000004: return 'Mixed Use';
            default: return 'Unknown';
        }
    }

    /**
     * Fetches all property data for the current entity
     * @returns Promise<Property[]>
     */
    public async fetchPropertyData(): Promise<Property[]> {
        console.log('🚀 Starting property data fetch process...');

        try {
            // Step 1: Get the main property
            const mainProperty = await this.fetchMainProperty();
            if (!mainProperty) {
                console.log('⚠️ No main property found, returning empty array');
                return [];
            }

            // Step 2: Get the main property ID (could be itself or a referenced main property)
            const mainPropertyId = mainProperty.cff_mainpropertyid || mainProperty.cff_propertyid;
            console.log('📋 Using main property ID:', mainPropertyId);

            // Step 3: Fetch all connected properties
            const connectedProperties = await this.fetchConnectedProperties(mainPropertyId);

            // Step 4: Combine main property with connected properties
            const allCrmProperties = [mainProperty, ...connectedProperties];

            // Step 5: Remove duplicates (in case main property is also in connected properties)
            const uniqueProperties = allCrmProperties.filter((property, index, array) => 
                array.findIndex(p => p.cff_propertyid === property.cff_propertyid) === index
            );

            // Step 6: Convert to Property objects
            const properties = uniqueProperties.map(crmProperty => 
                this.convertCrmPropertyToProperty(crmProperty)
            );

            console.log(`✅ Successfully fetched ${properties.length} properties from CRM`);
            console.log('📊 Property data:', properties);

            return properties;
        } catch (error) {
            console.error('❌ Error in fetchPropertyData:', error);
            throw error;
        }
    }
}
