/**
 * Property Data Service - Orchestrates data loading between development and production environments
 * Uses mock data in development, CRM data in production
 */

import { Property } from '../types/DiagramTypes';
import { isDevEnvironment, getCurrentEntityId } from './environmentUtils';
import { CrmDataService } from './crmDataService';

/**
 * Result interface for data loading operations
 */
export interface DataLoadResult {
    properties: Property[];
    success: boolean;
    source: 'mock' | 'crm';
    error?: string;
}

/**
 * Mock property data for development environment
 */
const getMockPropertyData = (): Property[] => {
    return [
        {
            id: "mega-park",
            name: "Mega Park",
            type: "Building",
            isMainProperty: true,
            metadata: {
                propertyUse: "Industrial",
                grossLeasableArea: 650.00
            }
        },
        {
            id: "floor-1",
            name: "Floor 1",
            type: "Floor",
            parentPropertyId: "mega-park",
            metadata: {
                propertyUse: "Business",
                grossLeasableArea: 3000.00
            }
        },
        {
            id: "floor-1-prop-1",
            name: "Floor 1 Prop 1",
            type: "Unit",
            parentPropertyId: "floor-1",
            metadata: {
                propertyUse: "Business",
                grossLeasableArea: 150.00
            }
        },
        {
            id: "floor-2",
            name: "Floor 2",
            type: "Floor",
            parentPropertyId: "floor-1",
            metadata: {
                propertyUse: "Business",
                grossLeasableArea: 3000.00
            }
        },
        {
            id: "floor-2-prop-1",
            name: "Floor 2 Prop 1",
            type: "Unit",
            parentPropertyId: "floor-2",
            metadata: {
                propertyUse: "Business",
                grossLeasableArea: 150.00
            }
        },
        {
            id: "floor-2-prop-2",
            name: "Floor 2 Prop 2",
            type: "Unit",
            parentPropertyId: "floor-2",
            metadata: {
                propertyUse: "Business",
                grossLeasableArea: 370.00
            }
        }
    ];
};

/**
 * Property Data Service class
 */
export class PropertyDataService {
    private context?: ComponentFramework.Context<unknown>;
    private entityId?: string | null;

    constructor(context?: ComponentFramework.Context<unknown>, entityId?: string | null) {
        this.context = context;
        this.entityId = entityId;
    }

    /**
     * Loads property data based on environment
     * @returns Promise<DataLoadResult>
     */
    public async loadPropertyData(): Promise<DataLoadResult> {
        console.log('🔄 PropertyDataService: Starting data load process...');

        // Check environment
        const isDev = isDevEnvironment(this.context);
        
        if (isDev) {
            return this.loadMockData();
        } else {
            return this.loadCrmData();
        }
    }

    /**
     * Loads mock data for development environment
     * @returns Promise<DataLoadResult>
     */
    private async loadMockData(): Promise<DataLoadResult> {
        console.log('🔧 PropertyDataService: Loading mock data for development');
        
        try {
            const properties = getMockPropertyData();
            
            console.log(`✅ Mock data loaded successfully: ${properties.length} properties`);
            
            return {
                properties,
                success: true,
                source: 'mock'
            };
        } catch (error) {
            console.error('❌ Error loading mock data:', error);
            
            return {
                properties: [],
                success: false,
                source: 'mock',
                error: error instanceof Error ? error.message : 'Unknown error loading mock data'
            };
        }
    }

    /**
     * Loads data from CRM for production environment
     * @returns Promise<DataLoadResult>
     */
    private async loadCrmData(): Promise<DataLoadResult> {
        console.log('🚀 PropertyDataService: Loading data from CRM');

        // Validate context and entity ID
        if (!this.context) {
            const error = 'No PCF context available for CRM data loading';
            console.error('❌', error);
            return this.fallbackToMockData(error);
        }

        const entityId = this.entityId || getCurrentEntityId(this.context);
        if (!entityId) {
            const error = 'No entity ID available for CRM data loading';
            console.error('❌', error);
            return this.fallbackToMockData(error);
        }

        try {
            // Create CRM data service and fetch data
            const crmService = new CrmDataService(this.context, entityId);
            const properties = await crmService.fetchPropertyData();

            if (properties.length === 0) {
                console.log('⚠️ No properties found in CRM, falling back to mock data');
                return this.fallbackToMockData('No properties found in CRM');
            }

            console.log(`✅ CRM data loaded successfully: ${properties.length} properties`);
            
            return {
                properties,
                success: true,
                source: 'crm'
            };
        } catch (error) {
            console.error('❌ Error loading CRM data:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error loading CRM data';
            return this.fallbackToMockData(errorMessage);
        }
    }

    /**
     * Falls back to mock data when CRM data loading fails
     * @param reason Reason for fallback
     * @returns Promise<DataLoadResult>
     */
    private async fallbackToMockData(reason: string): Promise<DataLoadResult> {
        console.log('⚠️ PropertyDataService: Falling back to mock data. Reason:', reason);
        
        try {
            const properties = getMockPropertyData();
            
            return {
                properties,
                success: false, // Mark as unsuccessful since we fell back
                source: 'mock',
                error: `CRM data loading failed: ${reason}. Using mock data as fallback.`
            };
        } catch (mockError) {
            console.error('❌ Even mock data loading failed:', mockError);
            
            return {
                properties: [],
                success: false,
                source: 'mock',
                error: `Both CRM and mock data loading failed. CRM error: ${reason}. Mock error: ${mockError instanceof Error ? mockError.message : 'Unknown mock error'}`
            };
        }
    }

    /**
     * Updates the context and entity ID for the service
     * @param context PCF context
     * @param entityId Entity ID
     */
    public updateContext(context: ComponentFramework.Context<unknown>, entityId?: string | null): void {
        this.context = context;
        this.entityId = entityId;
        console.log('🔄 PropertyDataService: Context updated', {
            hasContext: !!context,
            entityId: entityId || getCurrentEntityId(context)
        });
    }

    /**
     * Gets the current data source information
     * @returns Object with current configuration
     */
    public getDataSourceInfo() {
        const isDev = isDevEnvironment(this.context);
        const entityId = this.entityId || getCurrentEntityId(this.context);
        
        return {
            environment: isDev ? 'development' : 'production',
            dataSource: isDev ? 'mock' : 'crm',
            hasContext: !!this.context,
            entityId: entityId,
            contextValid: !!(this.context?.webAPI)
        };
    }
}
