/* Main container for the diagram component */
.diagram-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
}

/* GoJS diagram canvas styling */
.diagram-container canvas {
    outline: none;
    border-radius: 4px;
}

/* Diagram wrapper for proper sizing */
.diagram-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* Loading state */
.diagram-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    font-size: 14px;
}

/* Error state */
.diagram-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #d32f2f;
    font-size: 14px;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
    margin: 8px;
    padding: 16px;
}

/* Toolbar styling (if needed for future enhancements) */
.diagram-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    gap: 8px;
}

.diagram-toolbar button {
    padding: 6px 12px;
    border: 1px solid #d0d0d0;
    background-color: #ffffff;
    color: #333;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.diagram-toolbar button:hover {
    background-color: #f5f5f5;
    border-color: #b0b0b0;
}

.diagram-toolbar button:active {
    background-color: #e8e8e8;
}

.diagram-toolbar button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Status bar styling (if needed for future enhancements) */
.diagram-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 12px;
    background-color: #f0f0f0;
    border-top: 1px solid #e0e0e0;
    font-size: 11px;
    color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
    .diagram-toolbar {
        flex-wrap: wrap;
        padding: 6px 8px;
    }
    
    .diagram-toolbar button {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .diagram-container {
        border: 2px solid #000;
    }
    
    .diagram-toolbar button {
        border: 2px solid #000;
        background-color: #fff;
        color: #000;
    }
    
    .diagram-toolbar button:hover {
        background-color: #000;
        color: #fff;
    }
}

/* Focus styles for accessibility */
.diagram-container:focus-within {
    outline: 2px solid #0078d4;
    outline-offset: 2px;
}

/* Split layout styles */
.diagram-with-tree {
    display: flex;
    width: 100%;
    height: 100%;
}

.diagram-panel {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.tree-panel {
    width: 400px;
    min-width: 300px;
    max-width: 500px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Tree view styles */
.tree-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tree-header {
    padding: 12px 16px;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tree-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.tree-content {
    flex: 1;
    padding: 8px 0;
    overflow-y: auto;
}

.tree-node {
    margin: 0;
}

.tree-node-content {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 6px;
    margin: 2px 8px;
    font-size: 13px;
    min-height: 40px;
}

.tree-node-content:hover {
    background-color: #e3f2fd;
}

.tree-node-content.selected {
    background-color: #1976d2;
    color: white;
}

.tree-node-content.selected:hover {
    background-color: #1565c0;
}

.tree-expand-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    margin-top: 2px;
    font-size: 10px;
    cursor: pointer;
    border-radius: 2px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.tree-expand-icon:hover {
    background-color: rgba(0,0,0,0.1);
}

.tree-expand-spacer {
    width: 16px;
    margin-right: 8px;
    flex-shrink: 0;
}

.tree-node-info {
    flex: 1;
    min-width: 0;
}

.tree-node-main {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.tree-node-name {
    font-weight: 600;
    margin-right: 8px;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tree-node-type {
    font-size: 11px;
    color: #666;
    font-weight: normal;
    flex-shrink: 0;
}

.tree-node-content.selected .tree-node-type {
    color: rgba(255,255,255,0.8);
}

.tree-node-metadata {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.metadata-item {
    font-size: 11px;
    color: #888;
    font-weight: normal;
}

.tree-node-content.selected .metadata-item {
    color: rgba(255,255,255,0.7);
}

.tree-children {
    margin-left: 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .tree-panel {
        width: 300px;
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .diagram-with-tree {
        flex-direction: column;
    }

    .tree-panel {
        width: 100%;
        height: 250px;
        min-width: unset;
        max-width: unset;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .diagram-panel {
        flex: 1;
        border-left: none;
    }
}

/* Print styles */
@media print {
    .diagram-toolbar,
    .diagram-status {
        display: none;
    }

    .diagram-container {
        background-color: white;
        border: none;
    }

    .tree-panel {
        display: none;
    }

    .diagram-panel {
        width: 100% !important;
        border: none;
    }
}
