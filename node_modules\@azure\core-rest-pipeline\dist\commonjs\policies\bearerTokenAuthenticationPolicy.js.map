{"version": 3, "file": "bearerTokenAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/bearerTokenAuthenticationPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAuIlC,0EAoFC;AArND,2DAA2D;AAC3D,sCAAiD;AAEjD;;GAEG;AACU,QAAA,mCAAmC,GAAG,iCAAiC,CAAC;AA2FrF;;GAEG;AACH,KAAK,UAAU,uBAAuB,CAAC,OAAgC;IACrE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IACpD,MAAM,eAAe,GAAoB;QACvC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,cAAc,EAAE,OAAO,CAAC,cAAc;KACvC,CAAC;IACF,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAElE,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;IAC9E,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,QAA0B;IAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO;AACT,CAAC;AAED;;;GAGG;AACH,SAAgB,+BAA+B,CAC7C,OAA+C;;IAE/C,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;IAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,eAAU,CAAC;IAC5C,MAAM,SAAS,mBACb,gBAAgB,EAAE,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,gBAAgB,mCAAI,uBAAuB,EACjF,2BAA2B,EAAE,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,2BAA2B,IAEzE,kBAAkB,CACtB,CAAC;IAEF,iFAAiF;IACjF,qFAAqF;IACrF,wFAAwF;IACxF,iDAAiD;IACjD,MAAM,cAAc,GAAG,UAAU;QAC/B,CAAC,CAAC,IAAA,kCAAiB,EAAC,UAAU,CAAC,eAAe,CAAC;QAC/C,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhC,OAAO;QACL,IAAI,EAAE,2CAAmC;QACzC;;;;;;;;;;;;WAYG;QACH,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,CAAC,gBAAgB,CAAC;gBAC/B,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACjD,OAAO;gBACP,cAAc;gBACd,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,QAA0B,CAAC;YAC/B,IAAI,KAAwB,CAAC;YAC7B,IAAI,CAAC;gBACH,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,KAAK,GAAG,GAAG,CAAC;gBACZ,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC1B,CAAC;YAED,IACE,SAAS,CAAC,2BAA2B;gBACrC,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,MAAK,GAAG;gBACxB,YAAY,CAAC,QAAQ,CAAC,EACtB,CAAC;gBACD,sBAAsB;gBACtB,MAAM,iBAAiB,GAAG,MAAM,SAAS,CAAC,2BAA2B,CAAC;oBACpE,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBACjD,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,MAAM;iBACP,CAAC,CAAC;gBAEH,IAAI,iBAAiB,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport type { AzureLogger } from \"@azure/logger\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { createTokenCycler } from \"../util/tokenCycler.js\";\nimport { logger as coreLogger } from \"../log.js\";\n\n/**\n * The programmatic identifier of the bearerTokenAuthenticationPolicy.\n */\nexport const bearerTokenAuthenticationPolicyName = \"bearerTokenAuthenticationPolicy\";\n\n/**\n * Options sent to the authorizeRequest callback\n */\nexport interface AuthorizeRequestOptions {\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string[];\n  /**\n   * Function that retrieves either a cached access token or a new access token.\n   */\n  getAccessToken: (scopes: string[], options: GetTokenOptions) => Promise<AccessToken | null>;\n  /**\n   * Request that the policy is trying to fulfill.\n   */\n  request: PipelineRequest;\n  /**\n   * A logger, if one was sent through the HTTP pipeline.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Options sent to the authorizeRequestOnChallenge callback\n */\nexport interface AuthorizeRequestOnChallengeOptions {\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string[];\n  /**\n   * Function that retrieves either a cached access token or a new access token.\n   */\n  getAccessToken: (scopes: string[], options: GetTokenOptions) => Promise<AccessToken | null>;\n  /**\n   * Request that the policy is trying to fulfill.\n   */\n  request: PipelineRequest;\n  /**\n   * Response containing the challenge.\n   */\n  response: PipelineResponse;\n  /**\n   * A logger, if one was sent through the HTTP pipeline.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Options to override the processing of [Continuous Access Evaluation](https://docs.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation) challenges.\n */\nexport interface ChallengeCallbacks {\n  /**\n   * Allows for the authorization of the main request of this policy before it's sent.\n   */\n  authorizeRequest?(options: AuthorizeRequestOptions): Promise<void>;\n  /**\n   * Allows to handle authentication challenges and to re-authorize the request.\n   * The response containing the challenge is `options.response`.\n   * If this method returns true, the underlying request will be sent once again.\n   * The request may be modified before being sent.\n   */\n  authorizeRequestOnChallenge?(options: AuthorizeRequestOnChallengeOptions): Promise<boolean>;\n}\n\n/**\n * Options to configure the bearerTokenAuthenticationPolicy\n */\nexport interface BearerTokenAuthenticationPolicyOptions {\n  /**\n   * The TokenCredential implementation that can supply the bearer token.\n   */\n  credential?: TokenCredential;\n  /**\n   * The scopes for which the bearer token applies.\n   */\n  scopes: string | string[];\n  /**\n   * Allows for the processing of [Continuous Access Evaluation](https://docs.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation) challenges.\n   * If provided, it must contain at least the `authorizeRequestOnChallenge` method.\n   * If provided, after a request is sent, if it has a challenge, it can be processed to re-send the original request with the relevant challenge information.\n   */\n  challengeCallbacks?: ChallengeCallbacks;\n  /**\n   * A logger can be sent for debugging purposes.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * Default authorize request handler\n */\nasync function defaultAuthorizeRequest(options: AuthorizeRequestOptions): Promise<void> {\n  const { scopes, getAccessToken, request } = options;\n  const getTokenOptions: GetTokenOptions = {\n    abortSignal: request.abortSignal,\n    tracingOptions: request.tracingOptions,\n  };\n  const accessToken = await getAccessToken(scopes, getTokenOptions);\n\n  if (accessToken) {\n    options.request.headers.set(\"Authorization\", `Bearer ${accessToken.token}`);\n  }\n}\n\n/**\n * We will retrieve the challenge only if the response status code was 401,\n * and if the response contained the header \"WWW-Authenticate\" with a non-empty value.\n */\nfunction getChallenge(response: PipelineResponse): string | undefined {\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (response.status === 401 && challenge) {\n    return challenge;\n  }\n  return;\n}\n\n/**\n * A policy that can request a token from a TokenCredential implementation and\n * then apply it to the Authorization header of a request as a Bearer token.\n */\nexport function bearerTokenAuthenticationPolicy(\n  options: BearerTokenAuthenticationPolicyOptions,\n): PipelinePolicy {\n  const { credential, scopes, challengeCallbacks } = options;\n  const logger = options.logger || coreLogger;\n  const callbacks = {\n    authorizeRequest: challengeCallbacks?.authorizeRequest ?? defaultAuthorizeRequest,\n    authorizeRequestOnChallenge: challengeCallbacks?.authorizeRequestOnChallenge,\n    // keep all other properties\n    ...challengeCallbacks,\n  };\n\n  // This function encapsulates the entire process of reliably retrieving the token\n  // The options are left out of the public API until there's demand to configure this.\n  // Remember to extend `BearerTokenAuthenticationPolicyOptions` with `TokenCyclerOptions`\n  // in order to pass through the `options` object.\n  const getAccessToken = credential\n    ? createTokenCycler(credential /* , options */)\n    : () => Promise.resolve(null);\n\n  return {\n    name: bearerTokenAuthenticationPolicyName,\n    /**\n     * If there's no challenge parameter:\n     * - It will try to retrieve the token using the cache, or the credential's getToken.\n     * - Then it will try the next policy with or without the retrieved token.\n     *\n     * It uses the challenge parameters to:\n     * - Skip a first attempt to get the token from the credential if there's no cached token,\n     *   since it expects the token to be retrievable only after the challenge.\n     * - Prepare the outgoing request if the `prepareRequest` method has been provided.\n     * - Send an initial request to receive the challenge if it fails.\n     * - Process a challenge if the response contains it.\n     * - Retrieve a token with the challenge information, then re-send the request.\n     */\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!request.url.toLowerCase().startsWith(\"https://\")) {\n        throw new Error(\n          \"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\",\n        );\n      }\n\n      await callbacks.authorizeRequest({\n        scopes: Array.isArray(scopes) ? scopes : [scopes],\n        request,\n        getAccessToken,\n        logger,\n      });\n\n      let response: PipelineResponse;\n      let error: Error | undefined;\n      try {\n        response = await next(request);\n      } catch (err: any) {\n        error = err;\n        response = err.response;\n      }\n\n      if (\n        callbacks.authorizeRequestOnChallenge &&\n        response?.status === 401 &&\n        getChallenge(response)\n      ) {\n        // processes challenge\n        const shouldSendRequest = await callbacks.authorizeRequestOnChallenge({\n          scopes: Array.isArray(scopes) ? scopes : [scopes],\n          request,\n          response,\n          getAccessToken,\n          logger,\n        });\n\n        if (shouldSendRequest) {\n          return next(request);\n        }\n      }\n\n      if (error) {\n        throw error;\n      } else {\n        return response;\n      }\n    },\n  };\n}\n"]}