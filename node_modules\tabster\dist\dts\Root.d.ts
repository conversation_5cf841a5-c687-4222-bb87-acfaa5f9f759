/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { TabsterPart } from "./Utils";
export interface WindowWithTabsterInstance extends Window {
    __tabsterInstance?: Types.TabsterCore;
}
export declare class Root extends TabsterPart<Types.RootProps, undefined> implements Types.Root {
    readonly uid: string;
    private _dummyManager?;
    private _sys?;
    private _isFocused;
    private _setFocusedTimer;
    private _onDispose;
    constructor(tabster: Types.TabsterCore, element: HTMLElement, onDispose: (root: Root) => void, props: Types.RootProps, sys: Types.SysProps | undefined);
    addDummyInputs(): void;
    dispose(): void;
    moveOutWithDefaultAction(isBackward: boolean, relatedEvent: KeyboardEvent): void;
    private _setFocused;
    private _onFocusIn;
    private _onFocusOut;
    private _add;
    private _remove;
}
export declare class Root<PERSON>I implements Types.RootAPI {
    private _tabster;
    private _win;
    private _autoRoot;
    private _autoRootWaiting;
    private _roots;
    private _forceDummy;
    rootById: {
        [id: string]: Types.Root;
    };
    constructor(tabster: Types.TabsterCore, autoRoot?: Types.RootProps);
    private _autoRootCreate;
    private _autoRootUnwait;
    dispose(): void;
    createRoot(element: HTMLElement, props: Types.RootProps, sys: Types.SysProps | undefined): Types.Root;
    addDummyInputs(): void;
    static getRootByUId(getWindow: Types.GetWindow, id: string): Types.Root | undefined;
    /**
     * Fetches the tabster context for an element walking up its ancestors
     *
     * @param tabster Tabster instance
     * @param element The element the tabster context should represent
     * @param options Additional options
     * @returns undefined if the element is not a child of a tabster root, otherwise all applicable tabster behaviours and configurations
     */
    static getTabsterContext(tabster: Types.TabsterCore, element: Node, options?: Types.GetTabsterContextOptions): Types.TabsterContext | undefined;
    static getRoot(tabster: Types.TabsterCore, element: HTMLElement): Types.Root | undefined;
    onRoot(root: Types.Root, removed?: boolean): void;
    private _onRootDispose;
}
