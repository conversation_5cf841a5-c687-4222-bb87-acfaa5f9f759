/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
export declare const TABSTER_ATTRIBUTE_NAME: "data-tabster";
export declare const TABSTER_DUMMY_INPUT_ATTRIBUTE_NAME: "data-tabster-dummy";
export declare const FOCUSABLE_SELECTOR: string;
export declare const AsyncFocusSources: {
    readonly EscapeGroupper: 1;
    readonly Restorer: 2;
    readonly Deloser: 3;
};
export declare const ObservedElementAccessibilities: {
    readonly Any: 0;
    readonly Accessible: 1;
    readonly Focusable: 2;
};
export declare const ObservedElementRequestStatuses: {
    readonly Waiting: 0;
    readonly Succeeded: 1;
    readonly Canceled: 2;
    readonly TimedOut: 3;
};
export declare const RestoreFocusOrders: {
    readonly History: 0;
    readonly DeloserDefault: 1;
    readonly RootDefault: 2;
    readonly DeloserFirst: 3;
    readonly RootFirst: 4;
};
export declare const DeloserStrategies: {
    /**
     * If the focus is lost, the focus will be restored automatically using all available focus history.
     * This is the default strategy.
     */
    readonly Auto: 0;
    /**
     * If the focus is lost from this Deloser instance, the focus will not be restored automatically.
     * The application might listen to the event and restore the focus manually.
     * But if it is lost from another Deloser instance, the history of this Deloser could be used finding
     * the element to focus.
     */
    readonly Manual: 1;
};
export declare const Visibilities: {
    readonly Invisible: 0;
    readonly PartiallyVisible: 1;
    readonly Visible: 2;
};
export declare const RestorerTypes: {
    readonly Source: 0;
    readonly Target: 1;
};
export declare const MoverDirections: {
    readonly Both: 0;
    readonly Vertical: 1;
    readonly Horizontal: 2;
    readonly Grid: 3;
    readonly GridLinear: 4;
};
export declare const MoverKeys: {
    readonly ArrowUp: 1;
    readonly ArrowDown: 2;
    readonly ArrowLeft: 3;
    readonly ArrowRight: 4;
    readonly PageUp: 5;
    readonly PageDown: 6;
    readonly Home: 7;
    readonly End: 8;
};
export declare const GroupperTabbabilities: {
    readonly Unlimited: 0;
    readonly Limited: 1;
    readonly LimitedTrapFocus: 2;
};
export declare const GroupperMoveFocusActions: {
    readonly Enter: 1;
    readonly Escape: 2;
};
export declare const SysDummyInputsPositions: {
    readonly Auto: 0;
    readonly Inside: 1;
    readonly Outside: 2;
};
