{"name": "rx", "scripts": ["dist/rx.aggregates.js", "dist/rx.aggregates.map", "dist/rx.aggregates.min.js", "dist/rx.all.compat.js", "dist/rx.all.compat.map", "dist/rx.all.compat.min.js", "dist/rx.all.js", "dist/rx.all.min.js", "dist/rx.all.map", "dist/rx.async.js", "dist/rx.async.map", "dist/rx.async.min.js", "dist/rx.async.compat.js", "dist/rx.async.compat.map", "dist/rx.async.compat.min.js", "dist/rx.backpressure.js", "dist/rx.backpressure.map", "dist/rx.backpressure.min.js", "dist/rx.backpressure.js", "dist/rx.backpressure.map", "dist/rx.backpressure.min.js", "dist/rx.binding.js", "dist/rx.binding.map", "dist/rx.binding.min.js", "dist/rx.coincidence.js", "dist/rx.coincidence.map", "dist/rx.coincidence.min.js", "dist/rx.js", "dist/rx.map", "dist/rx.min.js", "dist/rx.compat.js", "dist/rx.compat.map", "dist/rx.compat.min.js", "dist/rx.experimental.js", "dist/rx.experimental.map", "dist/rx.experimental.min.js", "dist/rx.joinpatterns.js", "dist/rx.joinpatterns.map", "dist/rx.joinpatterns.min.js", "dist/rx.lite.js", "dist/rx.lite.map", "dist/rx.lite.min.js", "dist/rx.lite.compat.js", "dist/rx.lite.compat.map", "dist/rx.lite.compat.min.js", "dist/rx.lite.extras.js", "dist/rx.lite.extras.map", "dist/rx.lite.extras.min.js", "dist/rx.testing.js", "dist/rx.testing.map", "dist/rx.testing.min.js", "dist/rx.time.js", "dist/rx.time.map", "dist/rx.time.min.js", "dist/rx.virtualtime.js", "dist/rx.virtualtime.map", "dist/rx.virtualtime.min.js"]}