/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import * as EventsTypes from "./EventsTypes";
/**
 * Events sent by Tabster.
 */
export declare const TabsterFocusInEventName = "tabster:focusin";
export declare const TabsterFocusOutEventName = "tabster:focusout";
export declare const TabsterMoveFocusEventName = "tabster:movefocus";
/**
 * Events sent by Deloser.
 */
export declare const DeloserFocusLostEventName = "tabster:deloser:focus-lost";
/**
 * Events to be sent to Deloser by the application.
 */
export declare const DeloserRestoreFocusEventName = "tabster:deloser:restore-focus";
/**
 * Events sent by Modalizer.
 */
export declare const ModalizerActiveEventName = "tabster:modalizer:active";
export declare const ModalizerInactiveEventName = "tabster:modalizer:inactive";
export declare const ModalizerFocusInEventName = "tabster:modalizer:focusin";
export declare const ModalizerFocusOutEventName = "tabster:modalizer:focusout";
/**
 * Events sent by Mover.
 */
export declare const MoverStateEventName = "tabster:mover:state";
/**
 * Events to be sent to Mover by the application.
 */
export declare const MoverMoveFocusEventName = "tabster:mover:movefocus";
export declare const MoverMemorizedElementEventName = "tabster:mover:memorized-element";
/**
 * Events sent by Groupper.
 */
/**
 * Events to be sent to Groupper by the application.
 */
export declare const GroupperMoveFocusEventName = "tabster:groupper:movefocus";
/**
 * Events sent by Restorer.
 */
export declare const RestorerRestoreFocusEventName = "tabster:restorer:restore-focus";
/**
 * Events sent by Root.
 */
export declare const RootFocusEventName = "tabster:root:focus";
export declare const RootBlurEventName = "tabster:root:blur";
declare const CustomEvent_: {
    new <T>(type: string, eventInitDict?: CustomEventInit<T>): CustomEvent<T>;
    prototype: CustomEvent;
};
export declare abstract class TabsterCustomEvent<D> extends CustomEvent_<D> {
    /**
     * @deprecated use `detail`.
     */
    details?: D;
    constructor(type: string, detail?: D);
}
export declare class TabsterFocusInEvent extends TabsterCustomEvent<Types.FocusedElementDetail> {
    constructor(detail: Types.FocusedElementDetail);
}
export declare class TabsterFocusOutEvent extends TabsterCustomEvent<Types.FocusedElementDetail> {
    constructor(detail: Types.FocusedElementDetail);
}
export declare class TabsterMoveFocusEvent extends TabsterCustomEvent<EventsTypes.TabsterMoveFocusEventDetail> {
    constructor(detail: EventsTypes.TabsterMoveFocusEventDetail);
}
export declare class MoverStateEvent extends TabsterCustomEvent<Types.MoverElementState> {
    constructor(detail: Types.MoverElementState);
}
export declare class MoverMoveFocusEvent extends TabsterCustomEvent<EventsTypes.MoverMoveFocusEventDetail> {
    constructor(detail: EventsTypes.MoverMoveFocusEventDetail);
}
export declare class MoverMemorizedElementEvent extends TabsterCustomEvent<EventsTypes.MoverMemorizedElementEventDetail> {
    constructor(detail: EventsTypes.MoverMemorizedElementEventDetail);
}
export declare class GroupperMoveFocusEvent extends TabsterCustomEvent<EventsTypes.GroupperMoveFocusEventDetail> {
    constructor(detail: EventsTypes.GroupperMoveFocusEventDetail);
}
export declare class ModalizerActiveEvent extends TabsterCustomEvent<EventsTypes.ModalizerEventDetail> {
    constructor(detail: EventsTypes.ModalizerEventDetail);
}
export declare class ModalizerInactiveEvent extends TabsterCustomEvent<EventsTypes.ModalizerEventDetail> {
    constructor(detail: EventsTypes.ModalizerEventDetail);
}
export declare class DeloserFocusLostEvent extends TabsterCustomEvent<Types.DeloserElementActions> {
    constructor(detail: Types.DeloserElementActions);
}
export declare class DeloserRestoreFocusEvent extends TabsterCustomEvent<undefined> {
    constructor();
}
export declare class RestorerRestoreFocusEvent extends TabsterCustomEvent<undefined> {
    constructor();
}
export declare class RootFocusEvent extends TabsterCustomEvent<EventsTypes.RootFocusEventDetail> {
    constructor(detail: EventsTypes.RootFocusEventDetail);
}
export declare class RootBlurEvent extends TabsterCustomEvent<EventsTypes.RootFocusEventDetail> {
    constructor(detail: EventsTypes.RootFocusEventDetail);
}
declare global {
    interface GlobalEventHandlersEventMap {
        [TabsterFocusInEventName]: TabsterFocusInEvent;
        [TabsterFocusOutEventName]: TabsterFocusOutEvent;
        [TabsterMoveFocusEventName]: TabsterMoveFocusEvent;
        [MoverStateEventName]: MoverStateEvent;
        [MoverMoveFocusEventName]: MoverMoveFocusEvent;
        [MoverMemorizedElementEventName]: MoverMemorizedElementEvent;
        [GroupperMoveFocusEventName]: GroupperMoveFocusEvent;
        [ModalizerActiveEventName]: ModalizerActiveEvent;
        [ModalizerInactiveEventName]: ModalizerInactiveEvent;
        [DeloserFocusLostEventName]: DeloserFocusLostEvent;
        [DeloserRestoreFocusEventName]: DeloserRestoreFocusEvent;
        [RestorerRestoreFocusEventName]: RestorerRestoreFocusEvent;
        [RootFocusEventName]: RootFocusEvent;
        [RootBlurEventName]: RootBlurEvent;
    }
}
export {};
