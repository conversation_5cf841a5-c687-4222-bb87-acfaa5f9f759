/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
export declare class FocusableAPI implements Types.FocusableAPI {
    private _tabster;
    constructor(tabster: Types.TabsterCore);
    dispose(): void;
    getProps(element: HTMLElement): Types.FocusableProps;
    isFocusable(el: HTMLElement, includeProgrammaticallyFocusable?: boolean, noVisibleCheck?: boolean, noAccessibleCheck?: boolean): boolean;
    isVisible(el: HTMLElement): boolean;
    isAccessible(el: HTMLElement): boolean;
    private _isDisabled;
    private _isHidden;
    findFirst(options: Types.FindFirstProps, out?: Types.FindFocusableOutputProps): HTMLElement | null | undefined;
    findLast(options: Types.FindFirstProps, out?: Types.FindFocusableOutputProps): HTMLElement | null | undefined;
    findNext(options: Types.FindNextProps, out?: Types.FindFocusableOutputProps): HTMLElement | null | undefined;
    findPrev(options: Types.FindNextProps, out?: Types.FindFocusableOutputProps): HTMLElement | null | undefined;
    findDefault(options: Types.FindDefaultProps, out?: Types.FindFocusableOutputProps): HTMLElement | null;
    findAll(options: Types.FindAllProps): HTMLElement[];
    findElement(options: Types.FindFocusableProps, out?: Types.FindFocusableOutputProps): HTMLElement | null | undefined;
    private _findElements;
    private _acceptElement;
}
