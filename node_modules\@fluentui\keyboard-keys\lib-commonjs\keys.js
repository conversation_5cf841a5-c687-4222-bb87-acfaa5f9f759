"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AVRInput: function() {
        return AVRInput;
    },
    AVRPower: function() {
        return AVRPower;
    },
    Accept: function() {
        return Accept;
    },
    Again: function() {
        return Again;
    },
    AllCandidates: function() {
        return AllCandidates;
    },
    Alphanumeric: function() {
        return Alphanumeric;
    },
    Alt: function() {
        return Alt;
    },
    AltGraph: function() {
        return AltGraph;
    },
    AppSwitch: function() {
        return AppSwitch;
    },
    ArrowDown: function() {
        return ArrowDown;
    },
    ArrowLeft: function() {
        return ArrowLeft;
    },
    ArrowRight: function() {
        return ArrowRight;
    },
    ArrowUp: function() {
        return ArrowUp;
    },
    Attn: function() {
        return Attn;
    },
    AudioBalanceLeft: function() {
        return AudioBalanceLeft;
    },
    AudioBalanceRight: function() {
        return AudioBalanceRight;
    },
    AudioBassBoostDown: function() {
        return AudioBassBoostDown;
    },
    AudioBassBoostToggle: function() {
        return AudioBassBoostToggle;
    },
    AudioBassBoostUp: function() {
        return AudioBassBoostUp;
    },
    AudioFaderFront: function() {
        return AudioFaderFront;
    },
    AudioFaderRear: function() {
        return AudioFaderRear;
    },
    AudioSurroundModeNext: function() {
        return AudioSurroundModeNext;
    },
    AudioTrebleDown: function() {
        return AudioTrebleDown;
    },
    AudioTrebleUp: function() {
        return AudioTrebleUp;
    },
    AudioVolumeDown: function() {
        return AudioVolumeDown;
    },
    AudioVolumeMute: function() {
        return AudioVolumeMute;
    },
    AudioVolumeUp: function() {
        return AudioVolumeUp;
    },
    Backspace: function() {
        return Backspace;
    },
    BrightnessDown: function() {
        return BrightnessDown;
    },
    BrightnessUp: function() {
        return BrightnessUp;
    },
    BrowserBack: function() {
        return BrowserBack;
    },
    BrowserFavorites: function() {
        return BrowserFavorites;
    },
    BrowserForward: function() {
        return BrowserForward;
    },
    BrowserHome: function() {
        return BrowserHome;
    },
    BrowserRefresh: function() {
        return BrowserRefresh;
    },
    BrowserSearch: function() {
        return BrowserSearch;
    },
    BrowserStop: function() {
        return BrowserStop;
    },
    Call: function() {
        return Call;
    },
    Camera: function() {
        return Camera;
    },
    CameraFocus: function() {
        return CameraFocus;
    },
    Cancel: function() {
        return Cancel;
    },
    CapsLock: function() {
        return CapsLock;
    },
    ChannelDown: function() {
        return ChannelDown;
    },
    ChannelUp: function() {
        return ChannelUp;
    },
    Clear: function() {
        return Clear;
    },
    Close: function() {
        return Close;
    },
    ClosedCaptionToggle: function() {
        return ClosedCaptionToggle;
    },
    CodeInput: function() {
        return CodeInput;
    },
    ColorF0Red: function() {
        return ColorF0Red;
    },
    ColorF1Green: function() {
        return ColorF1Green;
    },
    ColorF2Yellow: function() {
        return ColorF2Yellow;
    },
    ColorF3Blue: function() {
        return ColorF3Blue;
    },
    ColorF4Grey: function() {
        return ColorF4Grey;
    },
    ColorF5Brown: function() {
        return ColorF5Brown;
    },
    Compose: function() {
        return Compose;
    },
    ContextMenu: function() {
        return ContextMenu;
    },
    Control: function() {
        return Control;
    },
    Convert: function() {
        return Convert;
    },
    Copy: function() {
        return Copy;
    },
    CrSel: function() {
        return CrSel;
    },
    Cut: function() {
        return Cut;
    },
    DVR: function() {
        return DVR;
    },
    Dead: function() {
        return Dead;
    },
    Delete: function() {
        return Delete;
    },
    Dimmer: function() {
        return Dimmer;
    },
    DisplaySwap: function() {
        return DisplaySwap;
    },
    Eisu: function() {
        return Eisu;
    },
    Eject: function() {
        return Eject;
    },
    End: function() {
        return End;
    },
    EndCall: function() {
        return EndCall;
    },
    Enter: function() {
        return Enter;
    },
    EraseEof: function() {
        return EraseEof;
    },
    Escape: function() {
        return Escape;
    },
    ExSel: function() {
        return ExSel;
    },
    Execute: function() {
        return Execute;
    },
    Exit: function() {
        return Exit;
    },
    F1: function() {
        return F1;
    },
    F10: function() {
        return F10;
    },
    F11: function() {
        return F11;
    },
    F12: function() {
        return F12;
    },
    F2: function() {
        return F2;
    },
    F3: function() {
        return F3;
    },
    F4: function() {
        return F4;
    },
    F5: function() {
        return F5;
    },
    F6: function() {
        return F6;
    },
    F7: function() {
        return F7;
    },
    F8: function() {
        return F8;
    },
    F9: function() {
        return F9;
    },
    FavoriteClear0: function() {
        return FavoriteClear0;
    },
    FavoriteClear1: function() {
        return FavoriteClear1;
    },
    FavoriteClear2: function() {
        return FavoriteClear2;
    },
    FavoriteClear3: function() {
        return FavoriteClear3;
    },
    FavoriteRecall0: function() {
        return FavoriteRecall0;
    },
    FavoriteRecall1: function() {
        return FavoriteRecall1;
    },
    FavoriteRecall2: function() {
        return FavoriteRecall2;
    },
    FavoriteRecall3: function() {
        return FavoriteRecall3;
    },
    FavoriteStore0: function() {
        return FavoriteStore0;
    },
    FavoriteStore1: function() {
        return FavoriteStore1;
    },
    FavoriteStore2: function() {
        return FavoriteStore2;
    },
    FavoriteStore3: function() {
        return FavoriteStore3;
    },
    FinalMode: function() {
        return FinalMode;
    },
    Find: function() {
        return Find;
    },
    Fn: function() {
        return Fn;
    },
    FnLock: function() {
        return FnLock;
    },
    GoBack: function() {
        return GoBack;
    },
    GoHome: function() {
        return GoHome;
    },
    GroupFirst: function() {
        return GroupFirst;
    },
    GroupLast: function() {
        return GroupLast;
    },
    GroupNext: function() {
        return GroupNext;
    },
    GroupPrevious: function() {
        return GroupPrevious;
    },
    Guide: function() {
        return Guide;
    },
    GuideNextDay: function() {
        return GuideNextDay;
    },
    GuidePreviousDay: function() {
        return GuidePreviousDay;
    },
    HangulMode: function() {
        return HangulMode;
    },
    HanjaMode: function() {
        return HanjaMode;
    },
    Hankaku: function() {
        return Hankaku;
    },
    HeadsetHook: function() {
        return HeadsetHook;
    },
    Help: function() {
        return Help;
    },
    Hibernate: function() {
        return Hibernate;
    },
    Hiragana: function() {
        return Hiragana;
    },
    HiraganaKatakana: function() {
        return HiraganaKatakana;
    },
    Home: function() {
        return Home;
    },
    Hyper: function() {
        return Hyper;
    },
    Info: function() {
        return Info;
    },
    Insert: function() {
        return Insert;
    },
    InstantReplay: function() {
        return InstantReplay;
    },
    JunjaMode: function() {
        return JunjaMode;
    },
    KanaMode: function() {
        return KanaMode;
    },
    KanjiMode: function() {
        return KanjiMode;
    },
    Katakana: function() {
        return Katakana;
    },
    Key11: function() {
        return Key11;
    },
    Key12: function() {
        return Key12;
    },
    LastNumberRedial: function() {
        return LastNumberRedial;
    },
    LaunchApplication1: function() {
        return LaunchApplication1;
    },
    LaunchApplication2: function() {
        return LaunchApplication2;
    },
    LaunchCalendar: function() {
        return LaunchCalendar;
    },
    LaunchContacts: function() {
        return LaunchContacts;
    },
    LaunchMail: function() {
        return LaunchMail;
    },
    LaunchMediaPlayer: function() {
        return LaunchMediaPlayer;
    },
    LaunchMusicPlayer: function() {
        return LaunchMusicPlayer;
    },
    LaunchPhone: function() {
        return LaunchPhone;
    },
    LaunchScreenSaver: function() {
        return LaunchScreenSaver;
    },
    LaunchSpreadsheet: function() {
        return LaunchSpreadsheet;
    },
    LaunchWebBrowser: function() {
        return LaunchWebBrowser;
    },
    LaunchWebCam: function() {
        return LaunchWebCam;
    },
    LaunchWordProcessor: function() {
        return LaunchWordProcessor;
    },
    Link: function() {
        return Link;
    },
    ListProgram: function() {
        return ListProgram;
    },
    LiveContent: function() {
        return LiveContent;
    },
    Lock: function() {
        return Lock;
    },
    LogOff: function() {
        return LogOff;
    },
    MailForward: function() {
        return MailForward;
    },
    MailReply: function() {
        return MailReply;
    },
    MailSend: function() {
        return MailSend;
    },
    MannerMode: function() {
        return MannerMode;
    },
    MediaApps: function() {
        return MediaApps;
    },
    MediaAudioTrack: function() {
        return MediaAudioTrack;
    },
    MediaClose: function() {
        return MediaClose;
    },
    MediaFastForward: function() {
        return MediaFastForward;
    },
    MediaLast: function() {
        return MediaLast;
    },
    MediaNextTrack: function() {
        return MediaNextTrack;
    },
    MediaPause: function() {
        return MediaPause;
    },
    MediaPlay: function() {
        return MediaPlay;
    },
    MediaPlayPause: function() {
        return MediaPlayPause;
    },
    MediaPreviousTrack: function() {
        return MediaPreviousTrack;
    },
    MediaRecord: function() {
        return MediaRecord;
    },
    MediaRewind: function() {
        return MediaRewind;
    },
    MediaSkipBackward: function() {
        return MediaSkipBackward;
    },
    MediaSkipForward: function() {
        return MediaSkipForward;
    },
    MediaStepBackward: function() {
        return MediaStepBackward;
    },
    MediaStepForward: function() {
        return MediaStepForward;
    },
    MediaStop: function() {
        return MediaStop;
    },
    MediaTopMenu: function() {
        return MediaTopMenu;
    },
    MediaTrackNext: function() {
        return MediaTrackNext;
    },
    MediaTrackPrevious: function() {
        return MediaTrackPrevious;
    },
    Meta: function() {
        return Meta;
    },
    MicrophoneToggle: function() {
        return MicrophoneToggle;
    },
    MicrophoneVolumeDown: function() {
        return MicrophoneVolumeDown;
    },
    MicrophoneVolumeMute: function() {
        return MicrophoneVolumeMute;
    },
    MicrophoneVolumeUp: function() {
        return MicrophoneVolumeUp;
    },
    ModeChange: function() {
        return ModeChange;
    },
    NavigateIn: function() {
        return NavigateIn;
    },
    NavigateNext: function() {
        return NavigateNext;
    },
    NavigateOut: function() {
        return NavigateOut;
    },
    NavigatePrevious: function() {
        return NavigatePrevious;
    },
    New: function() {
        return New;
    },
    NextCandidate: function() {
        return NextCandidate;
    },
    NextFavoriteChannel: function() {
        return NextFavoriteChannel;
    },
    NextUserProfile: function() {
        return NextUserProfile;
    },
    NonConvert: function() {
        return NonConvert;
    },
    Notification: function() {
        return Notification;
    },
    NumLock: function() {
        return NumLock;
    },
    OnDemand: function() {
        return OnDemand;
    },
    Open: function() {
        return Open;
    },
    PageDown: function() {
        return PageDown;
    },
    PageUp: function() {
        return PageUp;
    },
    Pairing: function() {
        return Pairing;
    },
    Paste: function() {
        return Paste;
    },
    Pause: function() {
        return Pause;
    },
    PinPDown: function() {
        return PinPDown;
    },
    PinPMove: function() {
        return PinPMove;
    },
    PinPToggle: function() {
        return PinPToggle;
    },
    PinPUp: function() {
        return PinPUp;
    },
    Play: function() {
        return Play;
    },
    PlaySpeedDown: function() {
        return PlaySpeedDown;
    },
    PlaySpeedReset: function() {
        return PlaySpeedReset;
    },
    PlaySpeedUp: function() {
        return PlaySpeedUp;
    },
    Power: function() {
        return Power;
    },
    PowerOff: function() {
        return PowerOff;
    },
    PreviousCandidate: function() {
        return PreviousCandidate;
    },
    Print: function() {
        return Print;
    },
    PrintScreen: function() {
        return PrintScreen;
    },
    Process: function() {
        return Process;
    },
    Props: function() {
        return Props;
    },
    RandomToggle: function() {
        return RandomToggle;
    },
    RcLowBattery: function() {
        return RcLowBattery;
    },
    RecordSpeedNext: function() {
        return RecordSpeedNext;
    },
    Redo: function() {
        return Redo;
    },
    RfBypass: function() {
        return RfBypass;
    },
    Romaji: function() {
        return Romaji;
    },
    STBInput: function() {
        return STBInput;
    },
    STBPower: function() {
        return STBPower;
    },
    Save: function() {
        return Save;
    },
    ScanChannelsToggle: function() {
        return ScanChannelsToggle;
    },
    ScreenModeNext: function() {
        return ScreenModeNext;
    },
    ScrollLock: function() {
        return ScrollLock;
    },
    Select: function() {
        return Select;
    },
    Settings: function() {
        return Settings;
    },
    Shift: function() {
        return Shift;
    },
    SingleCandidate: function() {
        return SingleCandidate;
    },
    Soft1: function() {
        return Soft1;
    },
    Soft2: function() {
        return Soft2;
    },
    Soft3: function() {
        return Soft3;
    },
    Soft4: function() {
        return Soft4;
    },
    Space: function() {
        return Space;
    },
    SpeechCorrectionList: function() {
        return SpeechCorrectionList;
    },
    SpeechInputToggle: function() {
        return SpeechInputToggle;
    },
    SpellCheck: function() {
        return SpellCheck;
    },
    SplitScreenToggle: function() {
        return SplitScreenToggle;
    },
    Standby: function() {
        return Standby;
    },
    Subtitle: function() {
        return Subtitle;
    },
    Super: function() {
        return Super;
    },
    Symbol: function() {
        return Symbol;
    },
    SymbolLock: function() {
        return SymbolLock;
    },
    TV: function() {
        return TV;
    },
    TV3DMode: function() {
        return TV3DMode;
    },
    TVAntennaCable: function() {
        return TVAntennaCable;
    },
    TVAudioDescription: function() {
        return TVAudioDescription;
    },
    TVAudioDescriptionMixDown: function() {
        return TVAudioDescriptionMixDown;
    },
    TVAudioDescriptionMixUp: function() {
        return TVAudioDescriptionMixUp;
    },
    TVContentsMenu: function() {
        return TVContentsMenu;
    },
    TVDataService: function() {
        return TVDataService;
    },
    TVInput: function() {
        return TVInput;
    },
    TVInputComponent1: function() {
        return TVInputComponent1;
    },
    TVInputComponent2: function() {
        return TVInputComponent2;
    },
    TVInputComposite1: function() {
        return TVInputComposite1;
    },
    TVInputComposite2: function() {
        return TVInputComposite2;
    },
    TVInputHDMI1: function() {
        return TVInputHDMI1;
    },
    TVInputHDMI2: function() {
        return TVInputHDMI2;
    },
    TVInputHDMI3: function() {
        return TVInputHDMI3;
    },
    TVInputHDMI4: function() {
        return TVInputHDMI4;
    },
    TVInputVGA1: function() {
        return TVInputVGA1;
    },
    TVMediaContext: function() {
        return TVMediaContext;
    },
    TVNetwork: function() {
        return TVNetwork;
    },
    TVNumberEntry: function() {
        return TVNumberEntry;
    },
    TVPower: function() {
        return TVPower;
    },
    TVRadioService: function() {
        return TVRadioService;
    },
    TVSatellite: function() {
        return TVSatellite;
    },
    TVSatelliteBS: function() {
        return TVSatelliteBS;
    },
    TVSatelliteCS: function() {
        return TVSatelliteCS;
    },
    TVSatelliteToggle: function() {
        return TVSatelliteToggle;
    },
    TVTerrestrialAnalog: function() {
        return TVTerrestrialAnalog;
    },
    TVTerrestrialDigital: function() {
        return TVTerrestrialDigital;
    },
    TVTimer: function() {
        return TVTimer;
    },
    Tab: function() {
        return Tab;
    },
    Teletext: function() {
        return Teletext;
    },
    Undo: function() {
        return Undo;
    },
    Unidentified: function() {
        return Unidentified;
    },
    VideoModeNext: function() {
        return VideoModeNext;
    },
    VoiceDial: function() {
        return VoiceDial;
    },
    WakeUp: function() {
        return WakeUp;
    },
    Wink: function() {
        return Wink;
    },
    Zenkaku: function() {
        return Zenkaku;
    },
    ZenkakuHankaku: function() {
        return ZenkakuHankaku;
    },
    ZoomIn: function() {
        return ZoomIn;
    },
    ZoomOut: function() {
        return ZoomOut;
    },
    ZoomToggle: function() {
        return ZoomToggle;
    }
});
const Alt = 'Alt';
const AltGraph = 'AltGraph';
const CapsLock = 'CapsLock';
const Control = 'Control';
const Fn = 'Fn';
const FnLock = 'FnLock';
const Meta = 'Meta';
const NumLock = 'NumLock';
const ScrollLock = 'ScrollLock';
const Shift = 'Shift';
const Symbol = 'Symbol';
const SymbolLock = 'SymbolLock';
const Hyper = 'Hyper';
const Super = 'Super';
const Enter = 'Enter';
const Space = ' ';
const Tab = 'Tab';
const ArrowDown = 'ArrowDown';
const ArrowLeft = 'ArrowLeft';
const ArrowRight = 'ArrowRight';
const ArrowUp = 'ArrowUp';
const End = 'End';
const Home = 'Home';
const PageDown = 'PageDown';
const PageUp = 'PageUp';
const Backspace = 'Backspace';
const Clear = 'Clear';
const Copy = 'Copy';
const CrSel = 'CrSel';
const Cut = 'Cut';
const Delete = 'Delete';
const EraseEof = 'EraseEof';
const ExSel = 'ExSel';
const Insert = 'Insert';
const Paste = 'Paste';
const Redo = 'Redo';
const Undo = 'Undo';
const Accept = 'Accept';
const Again = 'Again';
const Attn = 'Attn';
const Cancel = 'Cancel';
const ContextMenu = 'ContextMenu';
const Escape = 'Escape';
const Execute = 'Execute';
const Find = 'Find';
const Help = 'Help';
const Pause = 'Pause';
const Play = 'Play';
const Props = 'Props';
const Select = 'Select';
const ZoomIn = 'ZoomIn';
const ZoomOut = 'ZoomOut';
const BrightnessDown = 'BrightnessDown';
const BrightnessUp = 'BrightnessUp';
const Eject = 'Eject';
const LogOff = 'LogOff';
const Power = 'Power';
const PowerOff = 'PowerOff';
const PrintScreen = 'PrintScreen';
const Hibernate = 'Hibernate';
const Standby = 'Standby';
const WakeUp = 'WakeUp';
const AllCandidates = 'AllCandidates';
const Alphanumeric = 'Alphanumeric';
const CodeInput = 'CodeInput';
const Compose = 'Compose';
const Convert = 'Convert';
const Dead = 'Dead';
const FinalMode = 'FinalMode';
const GroupFirst = 'GroupFirst';
const GroupLast = 'GroupLast';
const GroupNext = 'GroupNext';
const GroupPrevious = 'GroupPrevious';
const ModeChange = 'ModeChange';
const NextCandidate = 'NextCandidate';
const NonConvert = 'NonConvert';
const PreviousCandidate = 'PreviousCandidate';
const Process = 'Process';
const SingleCandidate = 'SingleCandidate';
const HangulMode = 'HangulMode';
const HanjaMode = 'HanjaMode';
const JunjaMode = 'JunjaMode';
const Eisu = 'Eisu';
const Hankaku = 'Hankaku';
const Hiragana = 'Hiragana';
const HiraganaKatakana = 'HiraganaKatakana';
const KanaMode = 'KanaMode';
const KanjiMode = 'KanjiMode';
const Katakana = 'Katakana';
const Romaji = 'Romaji';
const Zenkaku = 'Zenkaku';
const ZenkakuHankaku = 'ZenkakuHankaku';
const F1 = 'F1';
const F2 = 'F2';
const F3 = 'F3';
const F4 = 'F4';
const F5 = 'F5';
const F6 = 'F6';
const F7 = 'F7';
const F8 = 'F8';
const F9 = 'F9';
const F10 = 'F10';
const F11 = 'F11';
const F12 = 'F12';
const Soft1 = 'Soft1';
const Soft2 = 'Soft2';
const Soft3 = 'Soft3';
const Soft4 = 'Soft4';
const ChannelDown = 'ChannelDown';
const ChannelUp = 'ChannelUp';
const Close = 'Close';
const MailForward = 'MailForward';
const MailReply = 'MailReply';
const MailSend = 'MailSend';
const MediaClose = 'MediaClose';
const MediaFastForward = 'MediaFastForward';
const MediaPause = 'MediaPause';
const MediaPlay = 'MediaPlay';
const MediaPlayPause = 'MediaPlayPause';
const MediaRecord = 'MediaRecord';
const MediaRewind = 'MediaRewind';
const MediaStop = 'MediaStop';
const MediaTrackNext = 'MediaTrackNext';
const MediaTrackPrevious = 'MediaTrackPrevious';
const New = 'New';
const Open = 'Open';
const Print = 'Print';
const Save = 'Save';
const SpellCheck = 'SpellCheck';
const Key11 = 'Key11';
const Key12 = 'Key12';
const AudioBalanceLeft = 'AudioBalanceLeft';
const AudioBalanceRight = 'AudioBalanceRight';
const AudioBassBoostDown = 'AudioBassBoostDown';
const AudioBassBoostToggle = 'AudioBassBoostToggle';
const AudioBassBoostUp = 'AudioBassBoostUp';
const AudioFaderFront = 'AudioFaderFront';
const AudioFaderRear = 'AudioFaderRear';
const AudioSurroundModeNext = 'AudioSurroundModeNext';
const AudioTrebleDown = 'AudioTrebleDown';
const AudioTrebleUp = 'AudioTrebleUp';
const AudioVolumeDown = 'AudioVolumeDown';
const AudioVolumeUp = 'AudioVolumeUp';
const AudioVolumeMute = 'AudioVolumeMute';
const MicrophoneToggle = 'MicrophoneToggle';
const MicrophoneVolumeDown = 'MicrophoneVolumeDown';
const MicrophoneVolumeUp = 'MicrophoneVolumeUp';
const MicrophoneVolumeMute = 'MicrophoneVolumeMute';
const SpeechCorrectionList = 'SpeechCorrectionList';
const SpeechInputToggle = 'SpeechInputToggle';
const LaunchApplication1 = 'LaunchApplication1';
const LaunchApplication2 = 'LaunchApplication2';
const LaunchCalendar = 'LaunchCalendar';
const LaunchContacts = 'LaunchContacts';
const LaunchMail = 'LaunchMail';
const LaunchMediaPlayer = 'LaunchMediaPlayer';
const LaunchMusicPlayer = 'LaunchMusicPlayer';
const LaunchPhone = 'LaunchPhone';
const LaunchScreenSaver = 'LaunchScreenSaver';
const LaunchSpreadsheet = 'LaunchSpreadsheet';
const LaunchWebBrowser = 'LaunchWebBrowser';
const LaunchWebCam = 'LaunchWebCam';
const LaunchWordProcessor = 'LaunchWordProcessor';
const BrowserBack = 'BrowserBack';
const BrowserFavorites = 'BrowserFavorites';
const BrowserForward = 'BrowserForward';
const BrowserHome = 'BrowserHome';
const BrowserRefresh = 'BrowserRefresh';
const BrowserSearch = 'BrowserSearch';
const BrowserStop = 'BrowserStop';
const AppSwitch = 'AppSwitch';
const Call = 'Call';
const Camera = 'Camera';
const CameraFocus = 'CameraFocus';
const EndCall = 'EndCall';
const GoBack = 'GoBack';
const GoHome = 'GoHome';
const HeadsetHook = 'HeadsetHook';
const LastNumberRedial = 'LastNumberRedial';
const Notification = 'Notification';
const MannerMode = 'MannerMode';
const VoiceDial = 'VoiceDial';
const TV = 'TV';
const TV3DMode = 'TV3DMode';
const TVAntennaCable = 'TVAntennaCable';
const TVAudioDescription = 'TVAudioDescription';
const TVAudioDescriptionMixDown = 'TVAudioDescriptionMixDown';
const TVAudioDescriptionMixUp = 'TVAudioDescriptionMixUp';
const TVContentsMenu = 'TVContentsMenu';
const TVDataService = 'TVDataService';
const TVInput = 'TVInput';
const TVInputComponent1 = 'TVInputComponent1';
const TVInputComponent2 = 'TVInputComponent2';
const TVInputComposite1 = 'TVInputComposite1';
const TVInputComposite2 = 'TVInputComposite2';
const TVInputHDMI1 = 'TVInputHDMI1';
const TVInputHDMI2 = 'TVInputHDMI2';
const TVInputHDMI3 = 'TVInputHDMI3';
const TVInputHDMI4 = 'TVInputHDMI4';
const TVInputVGA1 = 'TVInputVGA1';
const TVMediaContext = 'TVMediaContext';
const TVNetwork = 'TVNetwork';
const TVNumberEntry = 'TVNumberEntry';
const TVPower = 'TVPower';
const TVRadioService = 'TVRadioService';
const TVSatellite = 'TVSatellite';
const TVSatelliteBS = 'TVSatelliteBS';
const TVSatelliteCS = 'TVSatelliteCS';
const TVSatelliteToggle = 'TVSatelliteToggle';
const TVTerrestrialAnalog = 'TVTerrestrialAnalog';
const TVTerrestrialDigital = 'TVTerrestrialDigital';
const TVTimer = 'TVTimer';
const AVRInput = 'AVRInput';
const AVRPower = 'AVRPower';
const ColorF0Red = 'ColorF0Red';
const ColorF1Green = 'ColorF1Green';
const ColorF2Yellow = 'ColorF2Yellow';
const ColorF3Blue = 'ColorF3Blue';
const ColorF4Grey = 'ColorF4Grey';
const ColorF5Brown = 'ColorF5Brown';
const ClosedCaptionToggle = 'ClosedCaptionToggle';
const Dimmer = 'Dimmer';
const DisplaySwap = 'DisplaySwap';
const DVR = 'DVR';
const Exit = 'Exit';
const FavoriteClear0 = 'FavoriteClear0';
const FavoriteClear1 = 'FavoriteClear1';
const FavoriteClear2 = 'FavoriteClear2';
const FavoriteClear3 = 'FavoriteClear3';
const FavoriteRecall0 = 'FavoriteRecall0';
const FavoriteRecall1 = 'FavoriteRecall1';
const FavoriteRecall2 = 'FavoriteRecall2';
const FavoriteRecall3 = 'FavoriteRecall3';
const FavoriteStore0 = 'FavoriteStore0';
const FavoriteStore1 = 'FavoriteStore1';
const FavoriteStore2 = 'FavoriteStore2';
const FavoriteStore3 = 'FavoriteStore3';
const Guide = 'Guide';
const GuideNextDay = 'GuideNextDay';
const GuidePreviousDay = 'GuidePreviousDay';
const Info = 'Info';
const InstantReplay = 'InstantReplay';
const Link = 'Link';
const ListProgram = 'ListProgram';
const LiveContent = 'LiveContent';
const Lock = 'Lock';
const MediaApps = 'MediaApps';
const MediaAudioTrack = 'MediaAudioTrack';
const MediaLast = 'MediaLast';
const MediaSkipBackward = 'MediaSkipBackward';
const MediaSkipForward = 'MediaSkipForward';
const MediaStepBackward = 'MediaStepBackward';
const MediaStepForward = 'MediaStepForward';
const MediaTopMenu = 'MediaTopMenu';
const NavigateIn = 'NavigateIn';
const NavigateNext = 'NavigateNext';
const NavigateOut = 'NavigateOut';
const NavigatePrevious = 'NavigatePrevious';
const NextFavoriteChannel = 'NextFavoriteChannel';
const NextUserProfile = 'NextUserProfile';
const OnDemand = 'OnDemand';
const Pairing = 'Pairing';
const PinPDown = 'PinPDown';
const PinPMove = 'PinPMove';
const PinPToggle = 'PinPToggle';
const PinPUp = 'PinPUp';
const PlaySpeedDown = 'PlaySpeedDown';
const PlaySpeedReset = 'PlaySpeedReset';
const PlaySpeedUp = 'PlaySpeedUp';
const RandomToggle = 'RandomToggle';
const RcLowBattery = 'RcLowBattery';
const RecordSpeedNext = 'RecordSpeedNext';
const RfBypass = 'RfBypass';
const ScanChannelsToggle = 'ScanChannelsToggle';
const ScreenModeNext = 'ScreenModeNext';
const Settings = 'Settings';
const SplitScreenToggle = 'SplitScreenToggle';
const STBInput = 'STBInput';
const STBPower = 'STBPower';
const Subtitle = 'Subtitle';
const Teletext = 'Teletext';
const VideoModeNext = 'VideoModeNext';
const Wink = 'Wink';
const ZoomToggle = 'ZoomToggle';
const MediaNextTrack = 'MediaNextTrack';
const MediaPreviousTrack = 'MediaPreviousTrack';
const Unidentified = 'Unidentified';
