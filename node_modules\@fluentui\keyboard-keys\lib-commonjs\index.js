// https://github.com/microsoft/rushstack/issues/2780
// API extractor can't support namespace exports
// export * as keyCodes from './keyCodes';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AVRInput: function() {
        return _keys.AVRInput;
    },
    AVRPower: function() {
        return _keys.AVRPower;
    },
    Accept: function() {
        return _keys.Accept;
    },
    Again: function() {
        return _keys.Again;
    },
    AllCandidates: function() {
        return _keys.AllCandidates;
    },
    Alphanumeric: function() {
        return _keys.Alphanumeric;
    },
    Alt: function() {
        return _keys.Alt;
    },
    AltGraph: function() {
        return _keys.AltGraph;
    },
    AppSwitch: function() {
        return _keys.AppSwitch;
    },
    ArrowDown: function() {
        return _keys.ArrowDown;
    },
    ArrowLeft: function() {
        return _keys.ArrowLeft;
    },
    ArrowRight: function() {
        return _keys.ArrowRight;
    },
    ArrowUp: function() {
        return _keys.ArrowUp;
    },
    Attn: function() {
        return _keys.Attn;
    },
    AudioBalanceLeft: function() {
        return _keys.AudioBalanceLeft;
    },
    AudioBalanceRight: function() {
        return _keys.AudioBalanceRight;
    },
    AudioBassBoostDown: function() {
        return _keys.AudioBassBoostDown;
    },
    AudioBassBoostToggle: function() {
        return _keys.AudioBassBoostToggle;
    },
    AudioBassBoostUp: function() {
        return _keys.AudioBassBoostUp;
    },
    AudioFaderFront: function() {
        return _keys.AudioFaderFront;
    },
    AudioFaderRear: function() {
        return _keys.AudioFaderRear;
    },
    AudioSurroundModeNext: function() {
        return _keys.AudioSurroundModeNext;
    },
    AudioTrebleDown: function() {
        return _keys.AudioTrebleDown;
    },
    AudioTrebleUp: function() {
        return _keys.AudioTrebleUp;
    },
    AudioVolumeDown: function() {
        return _keys.AudioVolumeDown;
    },
    AudioVolumeMute: function() {
        return _keys.AudioVolumeMute;
    },
    AudioVolumeUp: function() {
        return _keys.AudioVolumeUp;
    },
    Backspace: function() {
        return _keys.Backspace;
    },
    BrightnessDown: function() {
        return _keys.BrightnessDown;
    },
    BrightnessUp: function() {
        return _keys.BrightnessUp;
    },
    BrowserBack: function() {
        return _keys.BrowserBack;
    },
    BrowserFavorites: function() {
        return _keys.BrowserFavorites;
    },
    BrowserForward: function() {
        return _keys.BrowserForward;
    },
    BrowserHome: function() {
        return _keys.BrowserHome;
    },
    BrowserRefresh: function() {
        return _keys.BrowserRefresh;
    },
    BrowserSearch: function() {
        return _keys.BrowserSearch;
    },
    BrowserStop: function() {
        return _keys.BrowserStop;
    },
    Call: function() {
        return _keys.Call;
    },
    Camera: function() {
        return _keys.Camera;
    },
    CameraFocus: function() {
        return _keys.CameraFocus;
    },
    Cancel: function() {
        return _keys.Cancel;
    },
    CapsLock: function() {
        return _keys.CapsLock;
    },
    ChannelDown: function() {
        return _keys.ChannelDown;
    },
    ChannelUp: function() {
        return _keys.ChannelUp;
    },
    Clear: function() {
        return _keys.Clear;
    },
    Close: function() {
        return _keys.Close;
    },
    ClosedCaptionToggle: function() {
        return _keys.ClosedCaptionToggle;
    },
    CodeInput: function() {
        return _keys.CodeInput;
    },
    ColorF0Red: function() {
        return _keys.ColorF0Red;
    },
    ColorF1Green: function() {
        return _keys.ColorF1Green;
    },
    ColorF2Yellow: function() {
        return _keys.ColorF2Yellow;
    },
    ColorF3Blue: function() {
        return _keys.ColorF3Blue;
    },
    ColorF4Grey: function() {
        return _keys.ColorF4Grey;
    },
    ColorF5Brown: function() {
        return _keys.ColorF5Brown;
    },
    Compose: function() {
        return _keys.Compose;
    },
    ContextMenu: function() {
        return _keys.ContextMenu;
    },
    Control: function() {
        return _keys.Control;
    },
    Convert: function() {
        return _keys.Convert;
    },
    Copy: function() {
        return _keys.Copy;
    },
    CrSel: function() {
        return _keys.CrSel;
    },
    Cut: function() {
        return _keys.Cut;
    },
    DVR: function() {
        return _keys.DVR;
    },
    Dead: function() {
        return _keys.Dead;
    },
    Delete: function() {
        return _keys.Delete;
    },
    Dimmer: function() {
        return _keys.Dimmer;
    },
    DisplaySwap: function() {
        return _keys.DisplaySwap;
    },
    Eisu: function() {
        return _keys.Eisu;
    },
    Eject: function() {
        return _keys.Eject;
    },
    End: function() {
        return _keys.End;
    },
    EndCall: function() {
        return _keys.EndCall;
    },
    Enter: function() {
        return _keys.Enter;
    },
    EraseEof: function() {
        return _keys.EraseEof;
    },
    Escape: function() {
        return _keys.Escape;
    },
    ExSel: function() {
        return _keys.ExSel;
    },
    Execute: function() {
        return _keys.Execute;
    },
    Exit: function() {
        return _keys.Exit;
    },
    F1: function() {
        return _keys.F1;
    },
    F10: function() {
        return _keys.F10;
    },
    F11: function() {
        return _keys.F11;
    },
    F12: function() {
        return _keys.F12;
    },
    F2: function() {
        return _keys.F2;
    },
    F3: function() {
        return _keys.F3;
    },
    F4: function() {
        return _keys.F4;
    },
    F5: function() {
        return _keys.F5;
    },
    F6: function() {
        return _keys.F6;
    },
    F7: function() {
        return _keys.F7;
    },
    F8: function() {
        return _keys.F8;
    },
    F9: function() {
        return _keys.F9;
    },
    FavoriteClear0: function() {
        return _keys.FavoriteClear0;
    },
    FavoriteClear1: function() {
        return _keys.FavoriteClear1;
    },
    FavoriteClear2: function() {
        return _keys.FavoriteClear2;
    },
    FavoriteClear3: function() {
        return _keys.FavoriteClear3;
    },
    FavoriteRecall0: function() {
        return _keys.FavoriteRecall0;
    },
    FavoriteRecall1: function() {
        return _keys.FavoriteRecall1;
    },
    FavoriteRecall2: function() {
        return _keys.FavoriteRecall2;
    },
    FavoriteRecall3: function() {
        return _keys.FavoriteRecall3;
    },
    FavoriteStore0: function() {
        return _keys.FavoriteStore0;
    },
    FavoriteStore1: function() {
        return _keys.FavoriteStore1;
    },
    FavoriteStore2: function() {
        return _keys.FavoriteStore2;
    },
    FavoriteStore3: function() {
        return _keys.FavoriteStore3;
    },
    FinalMode: function() {
        return _keys.FinalMode;
    },
    Find: function() {
        return _keys.Find;
    },
    Fn: function() {
        return _keys.Fn;
    },
    FnLock: function() {
        return _keys.FnLock;
    },
    GoBack: function() {
        return _keys.GoBack;
    },
    GoHome: function() {
        return _keys.GoHome;
    },
    GroupFirst: function() {
        return _keys.GroupFirst;
    },
    GroupLast: function() {
        return _keys.GroupLast;
    },
    GroupNext: function() {
        return _keys.GroupNext;
    },
    GroupPrevious: function() {
        return _keys.GroupPrevious;
    },
    Guide: function() {
        return _keys.Guide;
    },
    GuideNextDay: function() {
        return _keys.GuideNextDay;
    },
    GuidePreviousDay: function() {
        return _keys.GuidePreviousDay;
    },
    HangulMode: function() {
        return _keys.HangulMode;
    },
    HanjaMode: function() {
        return _keys.HanjaMode;
    },
    Hankaku: function() {
        return _keys.Hankaku;
    },
    HeadsetHook: function() {
        return _keys.HeadsetHook;
    },
    Help: function() {
        return _keys.Help;
    },
    Hibernate: function() {
        return _keys.Hibernate;
    },
    Hiragana: function() {
        return _keys.Hiragana;
    },
    HiraganaKatakana: function() {
        return _keys.HiraganaKatakana;
    },
    Home: function() {
        return _keys.Home;
    },
    Hyper: function() {
        return _keys.Hyper;
    },
    Info: function() {
        return _keys.Info;
    },
    Insert: function() {
        return _keys.Insert;
    },
    InstantReplay: function() {
        return _keys.InstantReplay;
    },
    JunjaMode: function() {
        return _keys.JunjaMode;
    },
    KanaMode: function() {
        return _keys.KanaMode;
    },
    KanjiMode: function() {
        return _keys.KanjiMode;
    },
    Katakana: function() {
        return _keys.Katakana;
    },
    Key11: function() {
        return _keys.Key11;
    },
    Key12: function() {
        return _keys.Key12;
    },
    LastNumberRedial: function() {
        return _keys.LastNumberRedial;
    },
    LaunchApplication1: function() {
        return _keys.LaunchApplication1;
    },
    LaunchApplication2: function() {
        return _keys.LaunchApplication2;
    },
    LaunchCalendar: function() {
        return _keys.LaunchCalendar;
    },
    LaunchContacts: function() {
        return _keys.LaunchContacts;
    },
    LaunchMail: function() {
        return _keys.LaunchMail;
    },
    LaunchMediaPlayer: function() {
        return _keys.LaunchMediaPlayer;
    },
    LaunchMusicPlayer: function() {
        return _keys.LaunchMusicPlayer;
    },
    LaunchPhone: function() {
        return _keys.LaunchPhone;
    },
    LaunchScreenSaver: function() {
        return _keys.LaunchScreenSaver;
    },
    LaunchSpreadsheet: function() {
        return _keys.LaunchSpreadsheet;
    },
    LaunchWebBrowser: function() {
        return _keys.LaunchWebBrowser;
    },
    LaunchWebCam: function() {
        return _keys.LaunchWebCam;
    },
    LaunchWordProcessor: function() {
        return _keys.LaunchWordProcessor;
    },
    Link: function() {
        return _keys.Link;
    },
    ListProgram: function() {
        return _keys.ListProgram;
    },
    LiveContent: function() {
        return _keys.LiveContent;
    },
    Lock: function() {
        return _keys.Lock;
    },
    LogOff: function() {
        return _keys.LogOff;
    },
    MailForward: function() {
        return _keys.MailForward;
    },
    MailReply: function() {
        return _keys.MailReply;
    },
    MailSend: function() {
        return _keys.MailSend;
    },
    MannerMode: function() {
        return _keys.MannerMode;
    },
    MediaApps: function() {
        return _keys.MediaApps;
    },
    MediaAudioTrack: function() {
        return _keys.MediaAudioTrack;
    },
    MediaClose: function() {
        return _keys.MediaClose;
    },
    MediaFastForward: function() {
        return _keys.MediaFastForward;
    },
    MediaLast: function() {
        return _keys.MediaLast;
    },
    MediaNextTrack: function() {
        return _keys.MediaNextTrack;
    },
    MediaPause: function() {
        return _keys.MediaPause;
    },
    MediaPlay: function() {
        return _keys.MediaPlay;
    },
    MediaPlayPause: function() {
        return _keys.MediaPlayPause;
    },
    MediaPreviousTrack: function() {
        return _keys.MediaPreviousTrack;
    },
    MediaRecord: function() {
        return _keys.MediaRecord;
    },
    MediaRewind: function() {
        return _keys.MediaRewind;
    },
    MediaSkipBackward: function() {
        return _keys.MediaSkipBackward;
    },
    MediaSkipForward: function() {
        return _keys.MediaSkipForward;
    },
    MediaStepBackward: function() {
        return _keys.MediaStepBackward;
    },
    MediaStepForward: function() {
        return _keys.MediaStepForward;
    },
    MediaStop: function() {
        return _keys.MediaStop;
    },
    MediaTopMenu: function() {
        return _keys.MediaTopMenu;
    },
    MediaTrackNext: function() {
        return _keys.MediaTrackNext;
    },
    MediaTrackPrevious: function() {
        return _keys.MediaTrackPrevious;
    },
    Meta: function() {
        return _keys.Meta;
    },
    MicrophoneToggle: function() {
        return _keys.MicrophoneToggle;
    },
    MicrophoneVolumeDown: function() {
        return _keys.MicrophoneVolumeDown;
    },
    MicrophoneVolumeMute: function() {
        return _keys.MicrophoneVolumeMute;
    },
    MicrophoneVolumeUp: function() {
        return _keys.MicrophoneVolumeUp;
    },
    ModeChange: function() {
        return _keys.ModeChange;
    },
    NavigateIn: function() {
        return _keys.NavigateIn;
    },
    NavigateNext: function() {
        return _keys.NavigateNext;
    },
    NavigateOut: function() {
        return _keys.NavigateOut;
    },
    NavigatePrevious: function() {
        return _keys.NavigatePrevious;
    },
    New: function() {
        return _keys.New;
    },
    NextCandidate: function() {
        return _keys.NextCandidate;
    },
    NextFavoriteChannel: function() {
        return _keys.NextFavoriteChannel;
    },
    NextUserProfile: function() {
        return _keys.NextUserProfile;
    },
    NonConvert: function() {
        return _keys.NonConvert;
    },
    Notification: function() {
        return _keys.Notification;
    },
    NumLock: function() {
        return _keys.NumLock;
    },
    OnDemand: function() {
        return _keys.OnDemand;
    },
    Open: function() {
        return _keys.Open;
    },
    PageDown: function() {
        return _keys.PageDown;
    },
    PageUp: function() {
        return _keys.PageUp;
    },
    Pairing: function() {
        return _keys.Pairing;
    },
    Paste: function() {
        return _keys.Paste;
    },
    Pause: function() {
        return _keys.Pause;
    },
    PinPDown: function() {
        return _keys.PinPDown;
    },
    PinPMove: function() {
        return _keys.PinPMove;
    },
    PinPToggle: function() {
        return _keys.PinPToggle;
    },
    PinPUp: function() {
        return _keys.PinPUp;
    },
    Play: function() {
        return _keys.Play;
    },
    PlaySpeedDown: function() {
        return _keys.PlaySpeedDown;
    },
    PlaySpeedReset: function() {
        return _keys.PlaySpeedReset;
    },
    PlaySpeedUp: function() {
        return _keys.PlaySpeedUp;
    },
    Power: function() {
        return _keys.Power;
    },
    PowerOff: function() {
        return _keys.PowerOff;
    },
    PreviousCandidate: function() {
        return _keys.PreviousCandidate;
    },
    Print: function() {
        return _keys.Print;
    },
    PrintScreen: function() {
        return _keys.PrintScreen;
    },
    Process: function() {
        return _keys.Process;
    },
    Props: function() {
        return _keys.Props;
    },
    RandomToggle: function() {
        return _keys.RandomToggle;
    },
    RcLowBattery: function() {
        return _keys.RcLowBattery;
    },
    RecordSpeedNext: function() {
        return _keys.RecordSpeedNext;
    },
    Redo: function() {
        return _keys.Redo;
    },
    RfBypass: function() {
        return _keys.RfBypass;
    },
    Romaji: function() {
        return _keys.Romaji;
    },
    STBInput: function() {
        return _keys.STBInput;
    },
    STBPower: function() {
        return _keys.STBPower;
    },
    Save: function() {
        return _keys.Save;
    },
    ScanChannelsToggle: function() {
        return _keys.ScanChannelsToggle;
    },
    ScreenModeNext: function() {
        return _keys.ScreenModeNext;
    },
    ScrollLock: function() {
        return _keys.ScrollLock;
    },
    Select: function() {
        return _keys.Select;
    },
    Settings: function() {
        return _keys.Settings;
    },
    Shift: function() {
        return _keys.Shift;
    },
    SingleCandidate: function() {
        return _keys.SingleCandidate;
    },
    Soft1: function() {
        return _keys.Soft1;
    },
    Soft2: function() {
        return _keys.Soft2;
    },
    Soft3: function() {
        return _keys.Soft3;
    },
    Soft4: function() {
        return _keys.Soft4;
    },
    Space: function() {
        return _keys.Space;
    },
    SpeechCorrectionList: function() {
        return _keys.SpeechCorrectionList;
    },
    SpeechInputToggle: function() {
        return _keys.SpeechInputToggle;
    },
    SpellCheck: function() {
        return _keys.SpellCheck;
    },
    SplitScreenToggle: function() {
        return _keys.SplitScreenToggle;
    },
    Standby: function() {
        return _keys.Standby;
    },
    Subtitle: function() {
        return _keys.Subtitle;
    },
    Super: function() {
        return _keys.Super;
    },
    Symbol: function() {
        return _keys.Symbol;
    },
    SymbolLock: function() {
        return _keys.SymbolLock;
    },
    TV: function() {
        return _keys.TV;
    },
    TV3DMode: function() {
        return _keys.TV3DMode;
    },
    TVAntennaCable: function() {
        return _keys.TVAntennaCable;
    },
    TVAudioDescription: function() {
        return _keys.TVAudioDescription;
    },
    TVAudioDescriptionMixDown: function() {
        return _keys.TVAudioDescriptionMixDown;
    },
    TVAudioDescriptionMixUp: function() {
        return _keys.TVAudioDescriptionMixUp;
    },
    TVContentsMenu: function() {
        return _keys.TVContentsMenu;
    },
    TVDataService: function() {
        return _keys.TVDataService;
    },
    TVInput: function() {
        return _keys.TVInput;
    },
    TVInputComponent1: function() {
        return _keys.TVInputComponent1;
    },
    TVInputComponent2: function() {
        return _keys.TVInputComponent2;
    },
    TVInputComposite1: function() {
        return _keys.TVInputComposite1;
    },
    TVInputComposite2: function() {
        return _keys.TVInputComposite2;
    },
    TVInputHDMI1: function() {
        return _keys.TVInputHDMI1;
    },
    TVInputHDMI2: function() {
        return _keys.TVInputHDMI2;
    },
    TVInputHDMI3: function() {
        return _keys.TVInputHDMI3;
    },
    TVInputHDMI4: function() {
        return _keys.TVInputHDMI4;
    },
    TVInputVGA1: function() {
        return _keys.TVInputVGA1;
    },
    TVMediaContext: function() {
        return _keys.TVMediaContext;
    },
    TVNetwork: function() {
        return _keys.TVNetwork;
    },
    TVNumberEntry: function() {
        return _keys.TVNumberEntry;
    },
    TVPower: function() {
        return _keys.TVPower;
    },
    TVRadioService: function() {
        return _keys.TVRadioService;
    },
    TVSatellite: function() {
        return _keys.TVSatellite;
    },
    TVSatelliteBS: function() {
        return _keys.TVSatelliteBS;
    },
    TVSatelliteCS: function() {
        return _keys.TVSatelliteCS;
    },
    TVSatelliteToggle: function() {
        return _keys.TVSatelliteToggle;
    },
    TVTerrestrialAnalog: function() {
        return _keys.TVTerrestrialAnalog;
    },
    TVTerrestrialDigital: function() {
        return _keys.TVTerrestrialDigital;
    },
    TVTimer: function() {
        return _keys.TVTimer;
    },
    Tab: function() {
        return _keys.Tab;
    },
    Teletext: function() {
        return _keys.Teletext;
    },
    Undo: function() {
        return _keys.Undo;
    },
    Unidentified: function() {
        return _keys.Unidentified;
    },
    VideoModeNext: function() {
        return _keys.VideoModeNext;
    },
    VoiceDial: function() {
        return _keys.VoiceDial;
    },
    WakeUp: function() {
        return _keys.WakeUp;
    },
    Wink: function() {
        return _keys.Wink;
    },
    Zenkaku: function() {
        return _keys.Zenkaku;
    },
    ZenkakuHankaku: function() {
        return _keys.ZenkakuHankaku;
    },
    ZoomIn: function() {
        return _keys.ZoomIn;
    },
    ZoomOut: function() {
        return _keys.ZoomOut;
    },
    ZoomToggle: function() {
        return _keys.ZoomToggle;
    },
    keyCodes: function() {
        return _keyCodes;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _keyCodes = /*#__PURE__*/ _interop_require_wildcard._(require("./keyCodes"));
const _keys = require("./keys");
