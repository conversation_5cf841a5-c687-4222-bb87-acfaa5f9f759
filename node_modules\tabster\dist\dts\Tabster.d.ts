/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { getDummyInputContainer } from "./Utils";
export { getDummyInputContainer };
export declare function forceCleanup(tabster: Types.Tabster): void;
/**
 * Creates an instance of Tabster, returns the current window instance if it already exists.
 */
export declare function createTabster(win: Window, props?: Types.TabsterCoreProps): Types.Tabster;
/**
 * Returns an instance of Tabster if it was created before or null.
 */
export declare function getTabster(win: Window): Types.Tabster | null;
export declare function getShadowDOMAPI(): Types.DOMAPI;
/**
 * Creates a new groupper instance or returns an existing one
 * @param tabster Tabster instance
 */
export declare function getGroupper(tabster: Types.Tabster): Types.GroupperAPI;
/**
 * Creates a new mover instance or returns an existing one
 * @param tabster Tabster instance
 */
export declare function getMover(tabster: Types.Tabster): Types.MoverAPI;
export declare function getOutline(tabster: Types.Tabster): Types.OutlineAPI;
/**
 * Creates a new new deloser instance or returns an existing one
 * @param tabster Tabster instance
 * @param props Deloser props
 */
export declare function getDeloser(tabster: Types.Tabster, props?: {
    autoDeloser: Types.DeloserProps;
}): Types.DeloserAPI;
/**
 * Creates a new modalizer instance or returns an existing one
 * @param tabster Tabster instance
 * @param alwaysAccessibleSelector When Modalizer is active, we put
 * aria-hidden to everything else to hide it from screen readers. This CSS
 * selector allows to exclude some elements from this behaviour. For example,
 * this could be used to exclude aria-live region with the application-wide
 * status announcements.
 * @param accessibleCheck An optional callback that will be called when
 * active Modalizer wants to hide an element that doesn't belong to it from
 * the screen readers by setting aria-hidden. Similar to alwaysAccessibleSelector
 * but allows to address the elements programmatically rather than with a selector.
 * If the callback returns true, the element will not receive aria-hidden.
 */
export declare function getModalizer(tabster: Types.Tabster, alwaysAccessibleSelector?: string, accessibleCheck?: Types.ModalizerElementAccessibleCheck): Types.ModalizerAPI;
export declare function getObservedElement(tabster: Types.Tabster): Types.ObservedElementAPI;
export declare function getCrossOrigin(tabster: Types.Tabster): Types.CrossOriginAPI;
export declare function getInternal(tabster: Types.Tabster): Types.InternalAPI;
export declare function getRestorer(tabster: Types.Tabster): Types.RestorerAPI;
export declare function disposeTabster(tabster: Types.Tabster, allInstances?: boolean): void;
/**
 * Returns an instance of Tabster if it already exists on the window .
 * @param win window instance that could contain an Tabster instance.
 */
export declare function getCurrentTabster(win: Window): Types.TabsterCore | undefined;
/**
 * Allows to make Tabster non operational. Intended for performance debugging (and other
 * kinds of debugging), you can switch Tabster off without changing the application code
 * that consumes it.
 * @param tabster a reference created by createTabster().
 * @param noop true if you want to make Tabster noop, false if you want to turn it back.
 */
export declare function makeNoOp(tabster: Types.Tabster, noop: boolean): void;
export declare function isNoOp(tabster: Types.TabsterCore): boolean;
