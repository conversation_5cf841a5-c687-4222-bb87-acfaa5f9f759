/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
declare const TABSTER_ATTRIBUTE_NAME: "data-tabster";
declare const TABSTER_DUMMY_INPUT_ATTRIBUTE_NAME: "data-tabster-dummy";
declare const FOCUSABLE_SELECTOR: string;
declare const AsyncFocusSources$1: {
    readonly EscapeGroupper: 1;
    readonly Restorer: 2;
    readonly Deloser: 3;
};
declare const ObservedElementAccessibilities$1: {
    readonly Any: 0;
    readonly Accessible: 1;
    readonly Focusable: 2;
};
declare const ObservedElementRequestStatuses$1: {
    readonly Waiting: 0;
    readonly Succeeded: 1;
    readonly Canceled: 2;
    readonly TimedOut: 3;
};
declare const RestoreFocusOrders$1: {
    readonly History: 0;
    readonly DeloserDefault: 1;
    readonly RootDefault: 2;
    readonly DeloserFirst: 3;
    readonly RootFirst: 4;
};
declare const DeloserStrategies$1: {
    /**
     * If the focus is lost, the focus will be restored automatically using all available focus history.
     * This is the default strategy.
     */
    readonly Auto: 0;
    /**
     * If the focus is lost from this Deloser instance, the focus will not be restored automatically.
     * The application might listen to the event and restore the focus manually.
     * But if it is lost from another Deloser instance, the history of this Deloser could be used finding
     * the element to focus.
     */
    readonly Manual: 1;
};
declare const Visibilities$1: {
    readonly Invisible: 0;
    readonly PartiallyVisible: 1;
    readonly Visible: 2;
};
declare const RestorerTypes$1: {
    readonly Source: 0;
    readonly Target: 1;
};
declare const MoverDirections$1: {
    readonly Both: 0;
    readonly Vertical: 1;
    readonly Horizontal: 2;
    readonly Grid: 3;
    readonly GridLinear: 4;
};
declare const MoverKeys$1: {
    readonly ArrowUp: 1;
    readonly ArrowDown: 2;
    readonly ArrowLeft: 3;
    readonly ArrowRight: 4;
    readonly PageUp: 5;
    readonly PageDown: 6;
    readonly Home: 7;
    readonly End: 8;
};
declare const GroupperTabbabilities$1: {
    readonly Unlimited: 0;
    readonly Limited: 1;
    readonly LimitedTrapFocus: 2;
};
declare const GroupperMoveFocusActions$1: {
    readonly Enter: 1;
    readonly Escape: 2;
};
declare const SysDummyInputsPositions$1: {
    readonly Auto: 0;
    readonly Inside: 1;
    readonly Outside: 2;
};

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

interface HTMLElementWithTabsterFlags extends HTMLElement {
    __tabsterElementFlags?: {
        /**
         * @deprecated This option is added to support interop between Fluent UI V9 and Fluent UI V8.
         * Once Fluent UI V8 is not supported anymore, this option should be removed.
         */
        noDirectAriaHidden?: boolean;
    };
}
interface TabsterDOMAttribute {
    [TABSTER_ATTRIBUTE_NAME]: string | undefined;
}
interface TabsterCoreProps {
    autoRoot?: RootProps;
    /**
     * Allows all tab key presses under the tabster root to be controlled by tabster
     * @default true
     */
    controlTab?: boolean;
    /**
     * When controlTab is false, Root doesn't have dummy inputs by default.
     * This option allows to enable dummy inputs on Root.
     */
    rootDummyInputs?: boolean;
    /**
     * A callback that will be called for the uncontrolled areas when Tabster wants
     * to know is the uncontrolled element wants complete control (for example it
     * is trapping focus) and Tabster should not interfere with handling Tab.
     * If the callback returns undefined, then the default behaviour is to return
     * the uncontrolled.completely value from the element. If the callback returns
     * non-undefined value, the callback's value will dominate the element's
     * uncontrolled.completely value.
     */
    checkUncontrolledCompletely?: (element: HTMLElement, completely: boolean) => boolean | undefined;
    /**
     * @deprecated use checkUncontrolledCompletely.
     */
    checkUncontrolledTrappingFocus?: (element: HTMLElement) => boolean;
    /**
     * Custom getter for parent elements. Defaults to the default .parentElement call
     * Currently only used to detect tabster contexts
     */
    getParent?(el: Node): Node | null;
    /**
     * Ability to redefine all DOM API calls used by Tabster. For example, for
     * ShadowDOM support.
     */
    DOMAPI?: Partial<DOMAPI>;
}
interface DOMAPI {
    createMutationObserver: (callback: MutationCallback) => MutationObserver;
    createTreeWalker(doc: Document, root: Node, whatToShow?: number, filter?: NodeFilter | null): TreeWalker;
    getParentNode(node: Node | null | undefined): ParentNode | null;
    getParentElement(element: HTMLElement | null | undefined): HTMLElement | null;
    nodeContains(parent: Node | null | undefined, child: Node | null | undefined): boolean;
    getActiveElement(doc: Document): Element | null;
    querySelector(element: ParentNode, selector: string): Element | null;
    querySelectorAll(element: ParentNode, selector: string): Element[];
    getElementById(doc: Document, id: string): HTMLElement | null;
    getFirstChild(node: Node | null | undefined): ChildNode | null;
    getLastChild(node: Node | null | undefined): ChildNode | null;
    getNextSibling(node: Node | null | undefined): ChildNode | null;
    getPreviousSibling(node: Node | null | undefined): ChildNode | null;
    getFirstElementChild(element: Element | null | undefined): Element | null;
    getLastElementChild(element: Element | null | undefined): Element | null;
    getNextElementSibling(element: Element | null | undefined): Element | null;
    getPreviousElementSibling(element: Element | null | undefined): Element | null;
    appendChild(parent: Node, child: Node): Node;
    insertBefore(parent: Node, child: Node, referenceChild: Node | null): Node;
    getSelection(ref: Node): Selection | null;
    getElementsByName(referenceElement: HTMLElement, name: string): NodeListOf<HTMLElement>;
}
type GetTabster = () => TabsterCore;
type GetWindow = () => Window;
type SubscribableCallback<A, B = undefined> = (val: A, detail: B) => void;
interface Disposable {
}
interface Subscribable<A, B = undefined> {
    subscribe(callback: SubscribableCallback<A, B>): void;
    unsubscribe(callback: SubscribableCallback<A, B>): void;
}
interface KeyboardNavigationState extends Subscribable<boolean>, Disposable {
    isNavigatingWithKeyboard(): boolean;
    setNavigatingWithKeyboard(isNavigatingWithKeyboard: boolean): void;
}
interface FocusedElementDetail {
    relatedTarget?: HTMLElement;
    isFocusedProgrammatically?: boolean;
    modalizerId?: string;
}

type AsyncFocusSources = typeof AsyncFocusSources$1;
type AsyncFocusSource = AsyncFocusSources[keyof AsyncFocusSources];
interface FocusedElementState extends Subscribable<HTMLElement | undefined, FocusedElementDetail>, Disposable {
    getFocusedElement(): HTMLElement | undefined;
    getLastFocusedElement(): HTMLElement | undefined;
    focus(element: HTMLElement, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean, preventScroll?: boolean): boolean;
    focusDefault(container: HTMLElement): boolean;
    focusFirst(props: FindFirstProps): boolean;
    focusLast(props: FindFirstProps): boolean;
    resetFocus(container: HTMLElement): boolean;
}
interface WeakHTMLElement<D = undefined> {
    get(): HTMLElement | undefined;
    getData(): D | undefined;
}
interface TabsterPart<P> {
    readonly id: string;
    getElement(): HTMLElement | undefined;
    getProps(): P;
    setProps(props: P): void;
}
interface TabsterPartWithFindNextTabbable {
    findNextTabbable(current?: HTMLElement, reference?: HTMLElement, isBackward?: boolean, ignoreAccessibility?: boolean): NextTabbable | null;
}
interface TabsterPartWithAcceptElement {
    acceptElement(element: HTMLElement, state: FocusableAcceptElementState): number | undefined;
}
interface ObservedElementProps {
    names: string[];
    details?: any;
}
interface ObservedElementDetails extends ObservedElementProps {
    accessibility?: ObservedElementAccessibility;
}

type ObservedElementAccessibilities = typeof ObservedElementAccessibilities$1;
type ObservedElementAccessibility = ObservedElementAccessibilities[keyof ObservedElementAccessibilities];

type ObservedElementRequestStatuses = typeof ObservedElementRequestStatuses$1;
type ObservedElementRequestStatus = ObservedElementRequestStatuses[keyof ObservedElementRequestStatuses];
interface ObservedElementAsyncRequest<T> {
    result: Promise<T>;
    cancel(): void;
    status?: ObservedElementRequestStatus;
}
interface ObservedElementAPIInternal {
}
interface ObservedElementAPI extends Subscribable<HTMLElement, ObservedElementDetails>, Disposable, ObservedElementAPIInternal {
    getElement(observedName: string, accessibility?: ObservedElementAccessibility): HTMLElement | null;
    waitElement(observedName: string, timeout: number, accessibility?: ObservedElementAccessibility): ObservedElementAsyncRequest<HTMLElement | null>;
    requestFocus(observedName: string, timeout: number, options?: Pick<FocusOptions, "preventScroll">): ObservedElementAsyncRequest<boolean>;
}
interface CrossOriginElement {
    readonly uid: string;
    readonly ownerId: string;
    readonly id?: string;
    readonly rootId?: string;
    readonly observedName?: string;
    readonly observedDetails?: string;
    focus(noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
}
interface CrossOriginSentTo {
    [id: string]: true;
}
interface CrossOriginTransactionTypes {
    Bootstrap: 1;
    FocusElement: 2;
    State: 3;
    GetElement: 4;
    RestoreFocusInDeloser: 5;
    Ping: 6;
}
type CrossOriginTransactionType = CrossOriginTransactionTypes[keyof CrossOriginTransactionTypes];
interface CrossOriginTransactionData<I, O> {
    transaction: string;
    type: CrossOriginTransactionType;
    isResponse: boolean;
    timestamp: number;
    owner: string;
    sentto: CrossOriginSentTo;
    timeout?: number;
    target?: string;
    beginData?: I;
    endData?: O;
}
type CrossOriginTransactionSend = (data: CrossOriginTransactionData<any, any>) => void;
interface CrossOriginMessage {
    data: CrossOriginTransactionData<any, any>;
    send: CrossOriginTransactionSend;
}
interface CrossOriginFocusedElementState extends Subscribable<CrossOriginElement | undefined, FocusedElementDetail>, Disposable {
    focus(element: CrossOriginElement, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
    focusById(elementId: string, rootId?: string, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
    focusByObservedName(observedName: string, timeout?: number, rootId?: string, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
}
interface CrossOriginObservedElementState extends Subscribable<CrossOriginElement, ObservedElementProps>, Disposable {
    getElement(observedName: string, accessibility?: ObservedElementAccessibility): Promise<CrossOriginElement | null>;
    waitElement(observedName: string, timeout: number, accessibility?: ObservedElementAccessibility): Promise<CrossOriginElement | null>;
    requestFocus(observedName: string, timeout: number): Promise<boolean>;
}
interface CrossOriginAPI {
    focusedElement: CrossOriginFocusedElementState;
    observedElement: CrossOriginObservedElementState;
    setup(sendUp?: CrossOriginTransactionSend | null): (msg: CrossOriginMessage) => void;
    isSetUp(): boolean;
    dispose(): void;
}
interface OutlineProps {
    areaClass: string;
    outlineClass: string;
    outlineColor: string;
    outlineWidth: number;
    zIndex: number;
}
interface OutlinedElementProps {
    isIgnored?: boolean;
}
interface OutlineAPI extends Disposable {
    setup(props?: Partial<OutlineProps>): void;
}
interface DeloserElementActions {
    focusDefault: () => boolean;
    focusFirst: () => boolean;
    resetFocus: () => boolean;
    clearHistory: (preserveExisting?: boolean) => void;
    setSnapshot: (index: number) => void;
    isActive: () => boolean;
}

type RestoreFocusOrders = typeof RestoreFocusOrders$1;
type RestoreFocusOrder = RestoreFocusOrders[keyof RestoreFocusOrders];

type DeloserStrategies = typeof DeloserStrategies$1;
type DeloserStrategy = DeloserStrategies[keyof DeloserStrategies];
interface DeloserProps {
    restoreFocusOrder?: RestoreFocusOrder;
    noSelectorCheck?: boolean;
    strategy?: DeloserStrategy;
}
interface Deloser extends TabsterPart<DeloserProps> {
    readonly uid: string;
    readonly strategy: DeloserStrategy;
    dispose(): void;
    isActive(): boolean;
    setActive(active: boolean): void;
    getActions(): DeloserElementActions;
    setSnapshot(index: number): void;
    focusFirst(): boolean;
    unshift(element: HTMLElement): void;
    focusDefault(): boolean;
    resetFocus(): boolean;
    findAvailable(): HTMLElement | null;
    clearHistory(preserveExisting?: boolean): void;
    customFocusLostHandler(element: HTMLElement): boolean;
}
type DeloserConstructor = (element: HTMLElement, props: DeloserProps) => Deloser;
interface DeloserInterfaceInternal {
}
interface DeloserAPI extends DeloserInterfaceInternal, Disposable {
    getActions(element: HTMLElement): DeloserElementActions | undefined;
    pause(): void;
    resume(restore?: boolean): void;
}
interface FocusableProps {
    isDefault?: boolean;
    isIgnored?: boolean;
    /**
     * Do not determine an element's focusability based on aria-disabled.
     */
    ignoreAriaDisabled?: boolean;
    /**
     * Exclude element (and all subelements) from Mover navigation.
     */
    excludeFromMover?: boolean;
    /**
     * Prevents tabster from handling the keydown event
     */
    ignoreKeydown?: {
        Tab?: boolean;
        Escape?: boolean;
        Enter?: boolean;
        ArrowUp?: boolean;
        ArrowDown?: boolean;
        ArrowLeft?: boolean;
        ArrowRight?: boolean;
        PageUp?: boolean;
        PageDown?: boolean;
        Home?: boolean;
        End?: boolean;
    };
}
interface RadioButtonGroup {
    name: string;
    buttons: Set<HTMLInputElement>;
    checked?: HTMLInputElement;
}
interface FocusableAcceptElementState {
    container: HTMLElement;
    modalizerUserId?: string;
    currentCtx?: TabsterContext;
    from: HTMLElement;
    fromCtx?: TabsterContext;
    isBackward?: boolean;
    found?: boolean;
    foundElement?: HTMLElement;
    foundBackward?: HTMLElement;
    rejectElementsFrom?: HTMLElement;
    uncontrolled?: HTMLElement;
    acceptCondition: (el: HTMLElement) => boolean;
    hasCustomCondition?: boolean;
    includeProgrammaticallyFocusable?: boolean;
    ignoreAccessibility?: boolean;
    cachedGrouppers: {
        [id: string]: {
            isActive: boolean | undefined;
            first?: HTMLElement | null;
        };
    };
    cachedRadioGroups: {
        [name: string]: RadioButtonGroup;
    };
    isFindAll?: boolean;
    /**
     * A flag that indicates that some focusable elements were skipped
     * during the search and the found element is not the one the browser
     * would normally focus if the user pressed Tab.
     */
    skippedFocusable?: boolean;
}
interface FindFocusableProps {
    /**
     * The container used for the search.
     */
    container: HTMLElement;
    /**
     * The elemet to start from.
     */
    currentElement?: HTMLElement;
    /**
     * See `referenceElement` of GetTabsterContextOptions for description.
     */
    referenceElement?: HTMLElement;
    /**
     * Includes elements that can be focused programmatically.
     */
    includeProgrammaticallyFocusable?: boolean;
    /**
     * Ignore accessibility check.
     */
    ignoreAccessibility?: boolean;
    /**
     * Take active modalizer into account when searching for elements
     * (the elements out of active modalizer will not be returned).
     */
    useActiveModalizer?: boolean;
    /**
     * Search withing the specified modality, null for everything outside of modalizers, string within
     * a specific id, undefined for search within the current application state.
     */
    modalizerId?: string | null;
    /**
     * If true, find previous element instead of the next one.
     */
    isBackward?: boolean;
    /**
     * @param el element visited.
     * @returns if an element should be accepted.
     */
    acceptCondition?(el: HTMLElement): boolean;
    /**
     * A callback that will be called for every focusable element found during findAll().
     * If false is returned from this callback, the search will stop.
     */
    onElement?: FindElementCallback;
}
interface FindFocusableOutputProps {
    /**
     * An output parameter. Will be true after the findNext/findPrev() call if some focusable
     * elements were skipped during the search and the result element not immediately next
     * focusable after the currentElement.
     */
    outOfDOMOrder?: boolean;
    /**
     * An output parameter. Will be true if the found element is uncontrolled.
     */
    uncontrolled?: HTMLElement | null;
}
type FindFirstProps = Pick<FindFocusableProps, "container" | "modalizerId" | "includeProgrammaticallyFocusable" | "useActiveModalizer" | "ignoreAccessibility">;
type FindNextProps = Pick<FindFocusableProps, "currentElement" | "referenceElement" | "container" | "modalizerId" | "includeProgrammaticallyFocusable" | "useActiveModalizer" | "ignoreAccessibility">;
type FindDefaultProps = Pick<FindFocusableProps, "container" | "modalizerId" | "includeProgrammaticallyFocusable" | "useActiveModalizer" | "ignoreAccessibility">;
type FindAllProps = Pick<FindFocusableProps, "container" | "modalizerId" | "currentElement" | "isBackward" | "includeProgrammaticallyFocusable" | "useActiveModalizer" | "acceptCondition" | "ignoreAccessibility" | "onElement">;
/**
 * A callback that is called for every found element during search. Returning false stops search.
 */
type FindElementCallback = (element: HTMLElement) => boolean;
interface FocusableAPI extends Disposable {
    getProps(element: HTMLElement): FocusableProps;
    isFocusable(element: HTMLElement, includeProgrammaticallyFocusable?: boolean, noVisibleCheck?: boolean, noAccessibleCheck?: boolean): boolean;
    isVisible(element: HTMLElement): boolean;
    isAccessible(element: HTMLElement): boolean;
    findFirst(options: FindFirstProps, out?: FindFocusableOutputProps): HTMLElement | null | undefined;
    findLast(options: FindFirstProps, out?: FindFocusableOutputProps): HTMLElement | null | undefined;
    findNext(options: FindNextProps, out?: FindFocusableOutputProps): HTMLElement | null | undefined;
    findPrev(options: FindNextProps, out?: FindFocusableOutputProps): HTMLElement | null | undefined;
    findDefault(options: FindDefaultProps, out?: FindFocusableOutputProps): HTMLElement | null;
    /**
     * @returns All focusables in a given context that satisfy an given condition
     */
    findAll(options: FindAllProps): HTMLElement[];
    findElement(options: FindFocusableProps, out?: FindFocusableOutputProps): HTMLElement | null | undefined;
}
interface DummyInputManager {
    moveOut: (backwards: boolean) => void;
    moveOutWithDefaultAction: (backwards: boolean, relatedEvent: KeyboardEvent) => void;
}

type Visibilities = typeof Visibilities$1;
type Visibility = Visibilities[keyof Visibilities];
interface MoverElementState {
    isCurrent: boolean | undefined;
    visibility: Visibility;
}

type RestorerTypes = typeof RestorerTypes$1;
type RestorerType = RestorerTypes[keyof RestorerTypes];

type MoverDirections = typeof MoverDirections$1;
type MoverDirection = MoverDirections[keyof MoverDirections];
interface NextTabbable {
    element: HTMLElement | null | undefined;
    uncontrolled?: HTMLElement | null;
    outOfDOMOrder?: boolean;
}
interface MoverProps {
    direction?: MoverDirection;
    memorizeCurrent?: boolean;
    tabbable?: boolean;
    /**
     * Whether to allow cyclic navigation in the mover
     * Can only be applied if navigationType is MoverKeys.Arrows
     *
     * @defaultValue false
     */
    cyclic?: boolean;
    /**
     * In case we need a rich state of the elements inside a Mover,
     * we can track it. It takes extra resourses and might affect
     * performance when a Mover has many elements inside, so make sure
     * you use this prop when it is really needed.
     */
    trackState?: boolean;
    /**
     * When set to Visibility.Visible or Visibility.PartiallyVisible,
     * uses the visibility part of the trackState prop to be able to
     * go to first/last visible element (instead of first/last focusable
     * element in DOM) when tabbing from outside of the mover.
     */
    visibilityAware?: Visibility;
    /**
     * When true, Mover will try to locate a focusable with Focusable.isDefault
     * property as a prioritized element to focus. True by default.
     */
    hasDefault?: boolean;
    /**
     * A value between 0 and 1 that specifies the tolerance allowed
     * when testing for visibility.
     *
     * @example
     * an element of height 100px has 10px that are above the viewport
     * hidden by scroll. This element is a valid visible element to focus.
     *
     * @default 0.8
     */
    visibilityTolerance?: number;
}
interface Mover extends TabsterPart<MoverProps>, TabsterPartWithFindNextTabbable, TabsterPartWithAcceptElement {
    readonly id: string;
    readonly dummyManager: DummyInputManager | undefined;
    readonly visibilityTolerance: NonNullable<MoverProps["visibilityTolerance"]>;
    dispose(): void;
    setCurrent(element: HTMLElement | undefined): void;
    getCurrent(): HTMLElement | null;
    getState(element: HTMLElement): MoverElementState | undefined;
}
type MoverConstructor = (tabster: TabsterCore, element: HTMLElement, props: MoverProps) => Mover;
interface MoverAPIInternal {
}

type MoverKeys = typeof MoverKeys$1;
type MoverKey = MoverKeys[keyof MoverKeys];
interface MoverAPI extends MoverAPIInternal, Disposable {
}

type GroupperTabbabilities = typeof GroupperTabbabilities$1;
type GroupperTabbability = GroupperTabbabilities[keyof GroupperTabbabilities];
interface GroupperProps {
    tabbability?: GroupperTabbability;
    delegated?: boolean;
}
interface Groupper extends TabsterPart<GroupperProps>, TabsterPartWithFindNextTabbable, TabsterPartWithAcceptElement {
    readonly id: string;
    readonly dummyManager: DummyInputManager | undefined;
    dispose(): void;
    makeTabbable(isUnlimited: boolean): void;
    isActive(noIfFirstIsFocused?: boolean): boolean | undefined;
    setFirst(element: HTMLElement | undefined): void;
    getFirst(orContainer: boolean): HTMLElement | undefined;
}
type GroupperConstructor = (tabster: TabsterCore, element: HTMLElement, props: GroupperProps) => Groupper;

type GroupperMoveFocusActions = typeof GroupperMoveFocusActions$1;
type GroupperMoveFocusAction = GroupperMoveFocusActions[keyof GroupperMoveFocusActions];
interface GroupperAPI extends GroupperAPIInternal, Disposable {
}
interface GroupperAPIInternal {
}
interface GroupperAPIInternal {
    forgetCurrentGrouppers(): void;
}
interface ModalizerProps {
    id: string;
    isOthersAccessible?: boolean;
    isAlwaysAccessible?: boolean;
    isNoFocusFirst?: boolean;
    isNoFocusDefault?: boolean;
    /** A focus trap variant, keeps focus inside the modal when tabbing */
    isTrapped?: boolean;
}
interface Modalizer extends TabsterPart<ModalizerProps>, TabsterPartWithFindNextTabbable {
    readonly userId: string;
    readonly dummyManager: DummyInputManager | undefined;
    /**
     * @returns - Whether the element is inside the modalizer
     */
    contains(element: HTMLElement): boolean;
    dispose(): void;
    isActive(): boolean;
    makeActive(isActive: boolean): void;
    focused(noIncrement?: boolean): number;
}
type ModalizerConstructor = (tabster: TabsterCore, element: HTMLElement, props: ModalizerProps) => Modalizer;
interface RootProps {
    restoreFocusOrder?: RestoreFocusOrder;
}
interface Root extends TabsterPart<RootProps> {
    readonly uid: string;
    dispose(): void;
    moveOutWithDefaultAction(backwards: boolean, relatedEvent: KeyboardEvent): void;
}
type RootConstructor = (tabster: TabsterCore, element: HTMLElement, props: RootProps) => Root;

type SysDummyInputsPositions = typeof SysDummyInputsPositions$1;
type SysDummyInputsPosition = SysDummyInputsPositions[keyof SysDummyInputsPositions];
/**
 * Ability to fine-tune Tabster internal behaviour in rare cases of need.
 * Normally, should not be used. A deep understanding of the intention and the effect
 * is required.
 */
interface SysProps {
    /**
     * Force dummy input position outside or inside of the element.
     * By default (when undefined), the position is determined dynamically
     * (for example inside for <li> elements and outside for <table> elements,
     * plus a default Groupper/Mover/Modalizer implementation position).
     * Setting to true will force the dummy inputs to be always outside of the element,
     * setting to false will force the dummy inputs to be always inside.
     */
    dummyInputsPosition?: SysDummyInputsPosition;
}
interface GetTabsterContextOptions {
    /**
     * Should visit **all** element ancestors to verify if `dir='rtl'` is set
     */
    checkRtl?: boolean;
    /**
     * The element to start computing the context from. Useful when dealing
     * with nested structures. For example, if we have an element inside a groupper
     * inside another groupper, the `groupper` prop in this element's contexts will
     * be the inner groupper, but when we pass the inner groupper's parent element
     * as `referenceElement`, the context groupper will be the outer one. Having
     * this option simplifies searching for the next tabbable element in the
     * environment of nested movers and grouppers.
     */
    referenceElement?: HTMLElement;
}
type TabsterContextMoverGroupper = {
    isMover: true;
    mover: Mover;
} | {
    isMover: false;
    groupper: Groupper;
};
interface TabsterContext {
    root: Root;
    modalizer?: Modalizer;
    groupper?: Groupper;
    mover?: Mover;
    groupperBeforeMover?: boolean;
    modalizerInGroupper?: Groupper;
    /**
     * Whether `dir='rtl'` is set on an ancestor
     */
    rtl?: boolean;
    excludedFromMover?: boolean;
    uncontrolled?: HTMLElement | null;
    ignoreKeydown: (e: KeyboardEvent) => boolean;
}
interface RootAPIInternal {
}
interface RootAPI extends Disposable, RootAPIInternal {
}
interface UncontrolledAPI {
    isUncontrolledCompletely(element: HTMLElement, completely: boolean): boolean;
}
interface ModalizerAPIInternal extends TabsterPartWithAcceptElement {
}
interface ModalizerAPI extends ModalizerAPIInternal, Disposable {
    /**
     * Activates a Modalizer and focuses the first or default element within
     *
     * @param elementFromModalizer An element that belongs to a Modalizer
     * @param noFocusFirst Do not focus on the first element in the Modalizer
     * @param noFocusDefault Do not focus the default element in the Modalizre
     */
    focus(elementFromModalizer: HTMLElement, noFocusFirst?: boolean, noFocusDefault?: boolean): boolean;
    /**
     * Just activates the modalizer without focusing on any element. Might be useful,
     * when the modalizer doesn't have focusable elements yet (but you want it active
     * already).
     *
     * @param modalizerElementOrContainer The element that belongs to a Modalizer or the Modalizer container,
     * or undefined to activate main app (deactivating any active modalizer).
     * @returns true if the modalizer was activated.
     */
    activate(modalizerElementOrContainer: HTMLElement | undefined): boolean;
}
interface RestorerAPIInternal {
}
interface RestorerAPI extends RestorerAPIInternal, Disposable {
}
interface Restorer extends Disposable, TabsterPart<RestorerProps> {
}
/**
 * A signature for the accessibleCheck callback from getModalizer().
 * It is called when active Modalizer sets aria-hidden on elements outsidef of it.
 *
 * @param element The element that is about to receive aria-hidden.
 * @param activeModalizerElements The container elements of the active modalizer.
 * @returns true if the element should remain accessible and should not receive
 * aria-hidden.
 */
type ModalizerElementAccessibleCheck = (element: HTMLElement, activeModalizerElements?: HTMLElement[]) => boolean;
interface UncontrolledProps {
    completely?: boolean;
}
interface DeloserOnElement {
    deloser: Deloser;
}
interface RootOnElement {
    root: Root;
}
interface ModalizerOnElement {
    modalizer: Modalizer;
}
interface RestorerOnElement {
    restorer: Restorer;
}
interface FocusableOnElement {
    focusable: FocusableProps;
}
interface MoverOnElement {
    mover: Mover;
}
interface GroupperOnElement {
    groupper: Groupper;
}
interface UncontrolledOnElement {
    uncontrolled: UncontrolledProps;
}
interface ObservedOnElement {
    observed: ObservedElementProps;
}
interface OutlineOnElement {
    outline: OutlinedElementProps;
}
interface SysOnElement {
    sys: SysProps;
}
interface RestorerProps {
    type: RestorerType;
    id?: string;
}
type TabsterAttributeProps = Partial<{
    deloser: DeloserProps;
    root: RootProps;
    uncontrolled: UncontrolledProps;
    modalizer: ModalizerProps;
    focusable: FocusableProps;
    groupper: GroupperProps;
    mover: MoverProps;
    observed: ObservedElementProps;
    outline: OutlinedElementProps;
    sys: SysProps;
    restorer: RestorerProps;
}>;
interface TabsterAttributeOnElement {
    string: string;
    object: TabsterAttributeProps;
}
interface TabsterAugmentedAttributes {
    [name: string]: string | null;
}
type TabsterOnElement = Partial<RootOnElement & DeloserOnElement & ModalizerOnElement & FocusableOnElement & MoverOnElement & GroupperOnElement & ObservedOnElement & OutlineOnElement & UncontrolledOnElement & SysOnElement & RestorerOnElement>;
interface OutlineElements {
    container: HTMLDivElement;
    left: HTMLDivElement;
    top: HTMLDivElement;
    right: HTMLDivElement;
    bottom: HTMLDivElement;
}
interface TabsterElementStorageEntry {
    tabster?: TabsterOnElement;
    attr?: TabsterAttributeOnElement;
    aug?: TabsterAugmentedAttributes;
}
interface TabsterElementStorage {
    [uid: string]: TabsterElementStorageEntry;
}
type DisposeFunc = () => void;
interface InternalAPI {
    stopObserver(): void;
    resumeObserver(syncState: boolean): void;
}
interface DummyInputObserver {
    add(dummy: HTMLElement, callback: () => void): void;
    remove(dummy: HTMLElement): void;
    dispose(): void;
    domChanged?(parent: HTMLElement): void;
    updatePositions(compute: (scrollTopLeftCache: Map<HTMLElement, {
        scrollTop: number;
        scrollLeft: number;
    } | null>) => () => void): void;
}
interface TabsterCoreInternal {
}
interface Tabster {
    keyboardNavigation: KeyboardNavigationState;
    focusedElement: FocusedElementState;
    focusable: FocusableAPI;
    root: RootAPI;
    uncontrolled: UncontrolledAPI;
}
interface TabsterCore extends Pick<TabsterCoreProps, "controlTab" | "rootDummyInputs">, Disposable, TabsterCoreInternal, Omit<Tabster, "core"> {
}
interface TabsterCompat {
    attributeTransform?: <P>(old: P) => TabsterAttributeProps;
}

type Types_d_AsyncFocusSource = AsyncFocusSource;
type Types_d_AsyncFocusSources = AsyncFocusSources;
type Types_d_CrossOriginAPI = CrossOriginAPI;
type Types_d_CrossOriginElement = CrossOriginElement;
type Types_d_CrossOriginFocusedElementState = CrossOriginFocusedElementState;
type Types_d_CrossOriginMessage = CrossOriginMessage;
type Types_d_CrossOriginObservedElementState = CrossOriginObservedElementState;
type Types_d_CrossOriginSentTo = CrossOriginSentTo;
type Types_d_CrossOriginTransactionData<I, O> = CrossOriginTransactionData<I, O>;
type Types_d_CrossOriginTransactionSend = CrossOriginTransactionSend;
type Types_d_CrossOriginTransactionType = CrossOriginTransactionType;
type Types_d_CrossOriginTransactionTypes = CrossOriginTransactionTypes;
type Types_d_DOMAPI = DOMAPI;
type Types_d_Deloser = Deloser;
type Types_d_DeloserAPI = DeloserAPI;
type Types_d_DeloserConstructor = DeloserConstructor;
type Types_d_DeloserElementActions = DeloserElementActions;
type Types_d_DeloserOnElement = DeloserOnElement;
type Types_d_DeloserProps = DeloserProps;
type Types_d_DeloserStrategies = DeloserStrategies;
type Types_d_DeloserStrategy = DeloserStrategy;
type Types_d_Disposable = Disposable;
type Types_d_DisposeFunc = DisposeFunc;
type Types_d_DummyInputManager = DummyInputManager;
type Types_d_DummyInputObserver = DummyInputObserver;
type Types_d_FindAllProps = FindAllProps;
type Types_d_FindDefaultProps = FindDefaultProps;
type Types_d_FindElementCallback = FindElementCallback;
type Types_d_FindFirstProps = FindFirstProps;
type Types_d_FindFocusableOutputProps = FindFocusableOutputProps;
type Types_d_FindFocusableProps = FindFocusableProps;
type Types_d_FindNextProps = FindNextProps;
type Types_d_FocusableAPI = FocusableAPI;
type Types_d_FocusableAcceptElementState = FocusableAcceptElementState;
type Types_d_FocusableOnElement = FocusableOnElement;
type Types_d_FocusableProps = FocusableProps;
type Types_d_FocusedElementDetail = FocusedElementDetail;
type Types_d_FocusedElementState = FocusedElementState;
type Types_d_GetTabster = GetTabster;
type Types_d_GetTabsterContextOptions = GetTabsterContextOptions;
type Types_d_GetWindow = GetWindow;
type Types_d_Groupper = Groupper;
type Types_d_GroupperAPI = GroupperAPI;
type Types_d_GroupperAPIInternal = GroupperAPIInternal;
type Types_d_GroupperConstructor = GroupperConstructor;
type Types_d_GroupperMoveFocusAction = GroupperMoveFocusAction;
type Types_d_GroupperMoveFocusActions = GroupperMoveFocusActions;
type Types_d_GroupperOnElement = GroupperOnElement;
type Types_d_GroupperProps = GroupperProps;
type Types_d_GroupperTabbabilities = GroupperTabbabilities;
type Types_d_GroupperTabbability = GroupperTabbability;
type Types_d_HTMLElementWithTabsterFlags = HTMLElementWithTabsterFlags;
type Types_d_InternalAPI = InternalAPI;
type Types_d_KeyboardNavigationState = KeyboardNavigationState;
type Types_d_Modalizer = Modalizer;
type Types_d_ModalizerAPI = ModalizerAPI;
type Types_d_ModalizerConstructor = ModalizerConstructor;
type Types_d_ModalizerElementAccessibleCheck = ModalizerElementAccessibleCheck;
type Types_d_ModalizerOnElement = ModalizerOnElement;
type Types_d_ModalizerProps = ModalizerProps;
type Types_d_Mover = Mover;
type Types_d_MoverAPI = MoverAPI;
type Types_d_MoverConstructor = MoverConstructor;
type Types_d_MoverDirection = MoverDirection;
type Types_d_MoverDirections = MoverDirections;
type Types_d_MoverElementState = MoverElementState;
type Types_d_MoverKey = MoverKey;
type Types_d_MoverKeys = MoverKeys;
type Types_d_MoverOnElement = MoverOnElement;
type Types_d_MoverProps = MoverProps;
type Types_d_NextTabbable = NextTabbable;
type Types_d_ObservedElementAPI = ObservedElementAPI;
type Types_d_ObservedElementAccessibilities = ObservedElementAccessibilities;
type Types_d_ObservedElementAccessibility = ObservedElementAccessibility;
type Types_d_ObservedElementAsyncRequest<T> = ObservedElementAsyncRequest<T>;
type Types_d_ObservedElementDetails = ObservedElementDetails;
type Types_d_ObservedElementProps = ObservedElementProps;
type Types_d_ObservedElementRequestStatus = ObservedElementRequestStatus;
type Types_d_ObservedElementRequestStatuses = ObservedElementRequestStatuses;
type Types_d_ObservedOnElement = ObservedOnElement;
type Types_d_OutlineAPI = OutlineAPI;
type Types_d_OutlineElements = OutlineElements;
type Types_d_OutlineOnElement = OutlineOnElement;
type Types_d_OutlineProps = OutlineProps;
type Types_d_OutlinedElementProps = OutlinedElementProps;
type Types_d_RadioButtonGroup = RadioButtonGroup;
type Types_d_RestoreFocusOrder = RestoreFocusOrder;
type Types_d_RestoreFocusOrders = RestoreFocusOrders;
type Types_d_Restorer = Restorer;
type Types_d_RestorerAPI = RestorerAPI;
type Types_d_RestorerOnElement = RestorerOnElement;
type Types_d_RestorerProps = RestorerProps;
type Types_d_RestorerType = RestorerType;
type Types_d_RestorerTypes = RestorerTypes;
type Types_d_Root = Root;
type Types_d_RootAPI = RootAPI;
type Types_d_RootConstructor = RootConstructor;
type Types_d_RootOnElement = RootOnElement;
type Types_d_RootProps = RootProps;
type Types_d_Subscribable<A, B = undefined> = Subscribable<A, B>;
type Types_d_SubscribableCallback<A, B = undefined> = SubscribableCallback<A, B>;
type Types_d_SysDummyInputsPosition = SysDummyInputsPosition;
type Types_d_SysDummyInputsPositions = SysDummyInputsPositions;
type Types_d_SysOnElement = SysOnElement;
type Types_d_SysProps = SysProps;
type Types_d_Tabster = Tabster;
type Types_d_TabsterAttributeOnElement = TabsterAttributeOnElement;
type Types_d_TabsterAttributeProps = TabsterAttributeProps;
type Types_d_TabsterAugmentedAttributes = TabsterAugmentedAttributes;
type Types_d_TabsterCompat = TabsterCompat;
type Types_d_TabsterContext = TabsterContext;
type Types_d_TabsterContextMoverGroupper = TabsterContextMoverGroupper;
type Types_d_TabsterCore = TabsterCore;
type Types_d_TabsterCoreProps = TabsterCoreProps;
type Types_d_TabsterDOMAttribute = TabsterDOMAttribute;
type Types_d_TabsterElementStorage = TabsterElementStorage;
type Types_d_TabsterElementStorageEntry = TabsterElementStorageEntry;
type Types_d_TabsterOnElement = TabsterOnElement;
type Types_d_TabsterPart<P> = TabsterPart<P>;
type Types_d_TabsterPartWithAcceptElement = TabsterPartWithAcceptElement;
type Types_d_TabsterPartWithFindNextTabbable = TabsterPartWithFindNextTabbable;
type Types_d_UncontrolledAPI = UncontrolledAPI;
type Types_d_UncontrolledOnElement = UncontrolledOnElement;
type Types_d_UncontrolledProps = UncontrolledProps;
type Types_d_Visibilities = Visibilities;
type Types_d_Visibility = Visibility;
type Types_d_WeakHTMLElement<D = undefined> = WeakHTMLElement<D>;
declare namespace Types_d {
  export type { Types_d_AsyncFocusSource as AsyncFocusSource, Types_d_AsyncFocusSources as AsyncFocusSources, Types_d_CrossOriginAPI as CrossOriginAPI, Types_d_CrossOriginElement as CrossOriginElement, Types_d_CrossOriginFocusedElementState as CrossOriginFocusedElementState, Types_d_CrossOriginMessage as CrossOriginMessage, Types_d_CrossOriginObservedElementState as CrossOriginObservedElementState, Types_d_CrossOriginSentTo as CrossOriginSentTo, Types_d_CrossOriginTransactionData as CrossOriginTransactionData, Types_d_CrossOriginTransactionSend as CrossOriginTransactionSend, Types_d_CrossOriginTransactionType as CrossOriginTransactionType, Types_d_CrossOriginTransactionTypes as CrossOriginTransactionTypes, Types_d_DOMAPI as DOMAPI, Types_d_Deloser as Deloser, Types_d_DeloserAPI as DeloserAPI, Types_d_DeloserConstructor as DeloserConstructor, Types_d_DeloserElementActions as DeloserElementActions, Types_d_DeloserOnElement as DeloserOnElement, Types_d_DeloserProps as DeloserProps, Types_d_DeloserStrategies as DeloserStrategies, Types_d_DeloserStrategy as DeloserStrategy, Types_d_Disposable as Disposable, Types_d_DisposeFunc as DisposeFunc, Types_d_DummyInputManager as DummyInputManager, Types_d_DummyInputObserver as DummyInputObserver, Types_d_FindAllProps as FindAllProps, Types_d_FindDefaultProps as FindDefaultProps, Types_d_FindElementCallback as FindElementCallback, Types_d_FindFirstProps as FindFirstProps, Types_d_FindFocusableOutputProps as FindFocusableOutputProps, Types_d_FindFocusableProps as FindFocusableProps, Types_d_FindNextProps as FindNextProps, Types_d_FocusableAPI as FocusableAPI, Types_d_FocusableAcceptElementState as FocusableAcceptElementState, Types_d_FocusableOnElement as FocusableOnElement, Types_d_FocusableProps as FocusableProps, Types_d_FocusedElementDetail as FocusedElementDetail, Types_d_FocusedElementState as FocusedElementState, Types_d_GetTabster as GetTabster, Types_d_GetTabsterContextOptions as GetTabsterContextOptions, Types_d_GetWindow as GetWindow, Types_d_Groupper as Groupper, Types_d_GroupperAPI as GroupperAPI, Types_d_GroupperAPIInternal as GroupperAPIInternal, Types_d_GroupperConstructor as GroupperConstructor, Types_d_GroupperMoveFocusAction as GroupperMoveFocusAction, Types_d_GroupperMoveFocusActions as GroupperMoveFocusActions, Types_d_GroupperOnElement as GroupperOnElement, Types_d_GroupperProps as GroupperProps, Types_d_GroupperTabbabilities as GroupperTabbabilities, Types_d_GroupperTabbability as GroupperTabbability, Types_d_HTMLElementWithTabsterFlags as HTMLElementWithTabsterFlags, Types_d_InternalAPI as InternalAPI, Types_d_KeyboardNavigationState as KeyboardNavigationState, Types_d_Modalizer as Modalizer, Types_d_ModalizerAPI as ModalizerAPI, Types_d_ModalizerConstructor as ModalizerConstructor, Types_d_ModalizerElementAccessibleCheck as ModalizerElementAccessibleCheck, Types_d_ModalizerOnElement as ModalizerOnElement, Types_d_ModalizerProps as ModalizerProps, Types_d_Mover as Mover, Types_d_MoverAPI as MoverAPI, Types_d_MoverConstructor as MoverConstructor, Types_d_MoverDirection as MoverDirection, Types_d_MoverDirections as MoverDirections, Types_d_MoverElementState as MoverElementState, Types_d_MoverKey as MoverKey, Types_d_MoverKeys as MoverKeys, Types_d_MoverOnElement as MoverOnElement, Types_d_MoverProps as MoverProps, Types_d_NextTabbable as NextTabbable, Types_d_ObservedElementAPI as ObservedElementAPI, Types_d_ObservedElementAccessibilities as ObservedElementAccessibilities, Types_d_ObservedElementAccessibility as ObservedElementAccessibility, Types_d_ObservedElementAsyncRequest as ObservedElementAsyncRequest, Types_d_ObservedElementDetails as ObservedElementDetails, Types_d_ObservedElementProps as ObservedElementProps, Types_d_ObservedElementRequestStatus as ObservedElementRequestStatus, Types_d_ObservedElementRequestStatuses as ObservedElementRequestStatuses, Types_d_ObservedOnElement as ObservedOnElement, Types_d_OutlineAPI as OutlineAPI, Types_d_OutlineElements as OutlineElements, Types_d_OutlineOnElement as OutlineOnElement, Types_d_OutlineProps as OutlineProps, Types_d_OutlinedElementProps as OutlinedElementProps, Types_d_RadioButtonGroup as RadioButtonGroup, Types_d_RestoreFocusOrder as RestoreFocusOrder, Types_d_RestoreFocusOrders as RestoreFocusOrders, Types_d_Restorer as Restorer, Types_d_RestorerAPI as RestorerAPI, Types_d_RestorerOnElement as RestorerOnElement, Types_d_RestorerProps as RestorerProps, Types_d_RestorerType as RestorerType, Types_d_RestorerTypes as RestorerTypes, Types_d_Root as Root, Types_d_RootAPI as RootAPI, Types_d_RootConstructor as RootConstructor, Types_d_RootOnElement as RootOnElement, Types_d_RootProps as RootProps, Types_d_Subscribable as Subscribable, Types_d_SubscribableCallback as SubscribableCallback, Types_d_SysDummyInputsPosition as SysDummyInputsPosition, Types_d_SysDummyInputsPositions as SysDummyInputsPositions, Types_d_SysOnElement as SysOnElement, Types_d_SysProps as SysProps, Types_d_Tabster as Tabster, Types_d_TabsterAttributeOnElement as TabsterAttributeOnElement, Types_d_TabsterAttributeProps as TabsterAttributeProps, Types_d_TabsterAugmentedAttributes as TabsterAugmentedAttributes, Types_d_TabsterCompat as TabsterCompat, Types_d_TabsterContext as TabsterContext, Types_d_TabsterContextMoverGroupper as TabsterContextMoverGroupper, Types_d_TabsterCore as TabsterCore, Types_d_TabsterCoreProps as TabsterCoreProps, Types_d_TabsterDOMAttribute as TabsterDOMAttribute, Types_d_TabsterElementStorage as TabsterElementStorage, Types_d_TabsterElementStorageEntry as TabsterElementStorageEntry, Types_d_TabsterOnElement as TabsterOnElement, Types_d_TabsterPart as TabsterPart, Types_d_TabsterPartWithAcceptElement as TabsterPartWithAcceptElement, Types_d_TabsterPartWithFindNextTabbable as TabsterPartWithFindNextTabbable, Types_d_UncontrolledAPI as UncontrolledAPI, Types_d_UncontrolledOnElement as UncontrolledOnElement, Types_d_UncontrolledProps as UncontrolledProps, Types_d_Visibilities as Visibilities, Types_d_Visibility as Visibility, Types_d_WeakHTMLElement as WeakHTMLElement };
}

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/**
 * If the passed element is Tabster dummy input, returns the container element this dummy input belongs to.
 * @param element Element to check for being dummy input.
 * @returns Dummy input container element (if the passed element is a dummy input) or null.
 */
declare function getDummyInputContainer(element: HTMLElement | null | undefined): HTMLElement | null;

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

declare function forceCleanup(tabster: Tabster): void;
/**
 * Creates an instance of Tabster, returns the current window instance if it already exists.
 */
declare function createTabster(win: Window, props?: TabsterCoreProps): Tabster;
/**
 * Returns an instance of Tabster if it was created before or null.
 */
declare function getTabster(win: Window): Tabster | null;
declare function getShadowDOMAPI(): DOMAPI;
/**
 * Creates a new groupper instance or returns an existing one
 * @param tabster Tabster instance
 */
declare function getGroupper(tabster: Tabster): GroupperAPI;
/**
 * Creates a new mover instance or returns an existing one
 * @param tabster Tabster instance
 */
declare function getMover(tabster: Tabster): MoverAPI;
declare function getOutline(tabster: Tabster): OutlineAPI;
/**
 * Creates a new new deloser instance or returns an existing one
 * @param tabster Tabster instance
 * @param props Deloser props
 */
declare function getDeloser(tabster: Tabster, props?: {
    autoDeloser: DeloserProps;
}): DeloserAPI;
/**
 * Creates a new modalizer instance or returns an existing one
 * @param tabster Tabster instance
 * @param alwaysAccessibleSelector When Modalizer is active, we put
 * aria-hidden to everything else to hide it from screen readers. This CSS
 * selector allows to exclude some elements from this behaviour. For example,
 * this could be used to exclude aria-live region with the application-wide
 * status announcements.
 * @param accessibleCheck An optional callback that will be called when
 * active Modalizer wants to hide an element that doesn't belong to it from
 * the screen readers by setting aria-hidden. Similar to alwaysAccessibleSelector
 * but allows to address the elements programmatically rather than with a selector.
 * If the callback returns true, the element will not receive aria-hidden.
 */
declare function getModalizer(tabster: Tabster, alwaysAccessibleSelector?: string, accessibleCheck?: ModalizerElementAccessibleCheck): ModalizerAPI;
declare function getObservedElement(tabster: Tabster): ObservedElementAPI;
declare function getCrossOrigin(tabster: Tabster): CrossOriginAPI;
declare function getInternal(tabster: Tabster): InternalAPI;
declare function getRestorer(tabster: Tabster): RestorerAPI;
declare function disposeTabster(tabster: Tabster, allInstances?: boolean): void;
/**
 * Allows to make Tabster non operational. Intended for performance debugging (and other
 * kinds of debugging), you can switch Tabster off without changing the application code
 * that consumes it.
 * @param tabster a reference created by createTabster().
 * @param noop true if you want to make Tabster noop, false if you want to turn it back.
 */
declare function makeNoOp(tabster: Tabster, noop: boolean): void;
declare function isNoOp(tabster: TabsterCore): boolean;

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

declare function getTabsterAttribute(props: TabsterAttributeProps): TabsterDOMAttribute;
declare function getTabsterAttribute(props: TabsterAttributeProps, plain: true): string;
/**
 * Updates Tabster props object with new props.
 * @param element an element to set data-tabster attribute on.
 * @param props current Tabster props to update.
 * @param newProps new Tabster props to add.
 *  When the value of a property in newProps is undefined, the property
 *  will be removed from the attribute.
 */
declare function mergeTabsterProps(props: TabsterAttributeProps, newProps: TabsterAttributeProps): void;
/**
 * Sets or updates Tabster attribute of the element.
 * @param element an element to set data-tabster attribute on.
 * @param newProps new Tabster props to set.
 * @param update if true, newProps will be merged with the existing props.
 *  When true and the value of a property in newProps is undefined, the property
 *  will be removed from the attribute.
 */
declare function setTabsterAttribute(element: HTMLElement, newProps: TabsterAttributeProps, update?: boolean): void;

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

interface TabsterMoveFocusEventDetail {
    by: "mover" | "groupper" | "modalizer" | "root" | "deloser";
    owner: HTMLElement;
    next: HTMLElement | null;
    relatedEvent?: KeyboardEvent;
}
interface MoverMoveFocusEventDetail {
    key: MoverKey;
}
interface MoverMemorizedElementEventDetail {
    memorizedElement: HTMLElement | undefined;
}
interface GroupperMoveFocusEventDetail {
    action: GroupperMoveFocusAction;
}
interface ModalizerEventDetail {
    id: string;
    element: HTMLElement;
}
interface RootFocusEventDetail {
    element: HTMLElement;
}

type EventsTypes_d_GroupperMoveFocusEventDetail = GroupperMoveFocusEventDetail;
type EventsTypes_d_ModalizerEventDetail = ModalizerEventDetail;
type EventsTypes_d_MoverMemorizedElementEventDetail = MoverMemorizedElementEventDetail;
type EventsTypes_d_MoverMoveFocusEventDetail = MoverMoveFocusEventDetail;
type EventsTypes_d_RootFocusEventDetail = RootFocusEventDetail;
type EventsTypes_d_TabsterMoveFocusEventDetail = TabsterMoveFocusEventDetail;
declare namespace EventsTypes_d {
  export type { EventsTypes_d_GroupperMoveFocusEventDetail as GroupperMoveFocusEventDetail, EventsTypes_d_ModalizerEventDetail as ModalizerEventDetail, EventsTypes_d_MoverMemorizedElementEventDetail as MoverMemorizedElementEventDetail, EventsTypes_d_MoverMoveFocusEventDetail as MoverMoveFocusEventDetail, EventsTypes_d_RootFocusEventDetail as RootFocusEventDetail, EventsTypes_d_TabsterMoveFocusEventDetail as TabsterMoveFocusEventDetail };
}

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/**
 * Events sent by Tabster.
 */
declare const TabsterFocusInEventName = "tabster:focusin";
declare const TabsterFocusOutEventName = "tabster:focusout";
declare const TabsterMoveFocusEventName = "tabster:movefocus";
/**
 * Events sent by Deloser.
 */
declare const DeloserFocusLostEventName = "tabster:deloser:focus-lost";
/**
 * Events to be sent to Deloser by the application.
 */
declare const DeloserRestoreFocusEventName = "tabster:deloser:restore-focus";
/**
 * Events sent by Modalizer.
 */
declare const ModalizerActiveEventName = "tabster:modalizer:active";
declare const ModalizerInactiveEventName = "tabster:modalizer:inactive";
declare const ModalizerFocusInEventName = "tabster:modalizer:focusin";
declare const ModalizerFocusOutEventName = "tabster:modalizer:focusout";
/**
 * Events sent by Mover.
 */
declare const MoverStateEventName = "tabster:mover:state";
/**
 * Events to be sent to Mover by the application.
 */
declare const MoverMoveFocusEventName = "tabster:mover:movefocus";
declare const MoverMemorizedElementEventName = "tabster:mover:memorized-element";
/**
 * Events sent by Groupper.
 */
/**
 * Events to be sent to Groupper by the application.
 */
declare const GroupperMoveFocusEventName = "tabster:groupper:movefocus";
/**
 * Events sent by Restorer.
 */
declare const RestorerRestoreFocusEventName = "tabster:restorer:restore-focus";
/**
 * Events sent by Root.
 */
declare const RootFocusEventName = "tabster:root:focus";
declare const RootBlurEventName = "tabster:root:blur";
declare const CustomEvent_: {
    new <T>(type: string, eventInitDict?: CustomEventInit<T>): CustomEvent<T>;
    prototype: CustomEvent;
};
declare abstract class TabsterCustomEvent<D> extends CustomEvent_<D> {
    /**
     * @deprecated use `detail`.
     */
    details?: D;
    constructor(type: string, detail?: D);
}
declare class TabsterFocusInEvent extends TabsterCustomEvent<FocusedElementDetail> {
    constructor(detail: FocusedElementDetail);
}
declare class TabsterFocusOutEvent extends TabsterCustomEvent<FocusedElementDetail> {
    constructor(detail: FocusedElementDetail);
}
declare class TabsterMoveFocusEvent extends TabsterCustomEvent<TabsterMoveFocusEventDetail> {
    constructor(detail: TabsterMoveFocusEventDetail);
}
declare class MoverStateEvent extends TabsterCustomEvent<MoverElementState> {
    constructor(detail: MoverElementState);
}
declare class MoverMoveFocusEvent extends TabsterCustomEvent<MoverMoveFocusEventDetail> {
    constructor(detail: MoverMoveFocusEventDetail);
}
declare class MoverMemorizedElementEvent extends TabsterCustomEvent<MoverMemorizedElementEventDetail> {
    constructor(detail: MoverMemorizedElementEventDetail);
}
declare class GroupperMoveFocusEvent extends TabsterCustomEvent<GroupperMoveFocusEventDetail> {
    constructor(detail: GroupperMoveFocusEventDetail);
}
declare class ModalizerActiveEvent extends TabsterCustomEvent<ModalizerEventDetail> {
    constructor(detail: ModalizerEventDetail);
}
declare class ModalizerInactiveEvent extends TabsterCustomEvent<ModalizerEventDetail> {
    constructor(detail: ModalizerEventDetail);
}
declare class DeloserFocusLostEvent extends TabsterCustomEvent<DeloserElementActions> {
    constructor(detail: DeloserElementActions);
}
declare class DeloserRestoreFocusEvent extends TabsterCustomEvent<undefined> {
    constructor();
}
declare class RestorerRestoreFocusEvent extends TabsterCustomEvent<undefined> {
    constructor();
}
declare class RootFocusEvent extends TabsterCustomEvent<RootFocusEventDetail> {
    constructor(detail: RootFocusEventDetail);
}
declare class RootBlurEvent extends TabsterCustomEvent<RootFocusEventDetail> {
    constructor(detail: RootFocusEventDetail);
}
declare global {
    interface GlobalEventHandlersEventMap {
        [TabsterFocusInEventName]: TabsterFocusInEvent;
        [TabsterFocusOutEventName]: TabsterFocusOutEvent;
        [TabsterMoveFocusEventName]: TabsterMoveFocusEvent;
        [MoverStateEventName]: MoverStateEvent;
        [MoverMoveFocusEventName]: MoverMoveFocusEvent;
        [MoverMemorizedElementEventName]: MoverMemorizedElementEvent;
        [GroupperMoveFocusEventName]: GroupperMoveFocusEvent;
        [ModalizerActiveEventName]: ModalizerActiveEvent;
        [ModalizerInactiveEventName]: ModalizerInactiveEvent;
        [DeloserFocusLostEventName]: DeloserFocusLostEvent;
        [DeloserRestoreFocusEventName]: DeloserRestoreFocusEvent;
        [RestorerRestoreFocusEventName]: RestorerRestoreFocusEvent;
        [RootFocusEventName]: RootFocusEvent;
        [RootBlurEventName]: RootBlurEvent;
    }
}

/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/** @deprecated This function is obsolete, use native element.dispatchEvent(new GroupperMoveFocusEvent(...)). */
declare function dispatchGroupperMoveFocusEvent(target: HTMLElement, action: GroupperMoveFocusAction): boolean;
/** @deprecated This function is obsolete, use native element.dispatchEvent(new MoverMoveFocusEvent(...)). */
declare function dispatchMoverMoveFocusEvent(target: HTMLElement, key: MoverKey): boolean;
/** @deprecated This function is obsolete, use native element.dispatchEvent(new MoverMemorizedElementEvent(...)). */
declare function dispatchMoverMemorizedElementEvent(target: HTMLElement, memorizedElement: HTMLElement | undefined): boolean;

export { AsyncFocusSources$1 as AsyncFocusSources, DeloserFocusLostEvent, DeloserFocusLostEventName, DeloserRestoreFocusEvent, DeloserRestoreFocusEventName, DeloserStrategies$1 as DeloserStrategies, EventsTypes_d as EventsTypes, FOCUSABLE_SELECTOR, GroupperMoveFocusActions$1 as GroupperMoveFocusActions, GroupperMoveFocusEvent, GroupperMoveFocusEventName, GroupperTabbabilities$1 as GroupperTabbabilities, ModalizerActiveEvent, ModalizerActiveEventName, ModalizerFocusInEventName, ModalizerFocusOutEventName, ModalizerInactiveEvent, ModalizerInactiveEventName, MoverDirections$1 as MoverDirections, MoverKeys$1 as MoverKeys, MoverMemorizedElementEvent, MoverMemorizedElementEventName, MoverMoveFocusEvent, MoverMoveFocusEventName, MoverStateEvent, MoverStateEventName, ObservedElementAccessibilities$1 as ObservedElementAccessibilities, ObservedElementRequestStatuses$1 as ObservedElementRequestStatuses, RestoreFocusOrders$1 as RestoreFocusOrders, RestorerRestoreFocusEvent, RestorerRestoreFocusEventName, RestorerTypes$1 as RestorerTypes, RootBlurEvent, RootBlurEventName, RootFocusEvent, RootFocusEventName, SysDummyInputsPositions$1 as SysDummyInputsPositions, TABSTER_ATTRIBUTE_NAME, TABSTER_DUMMY_INPUT_ATTRIBUTE_NAME, TabsterCustomEvent, TabsterFocusInEvent, TabsterFocusInEventName, TabsterFocusOutEvent, TabsterFocusOutEventName, TabsterMoveFocusEvent, TabsterMoveFocusEventName, Types_d as Types, Visibilities$1 as Visibilities, createTabster, dispatchGroupperMoveFocusEvent, dispatchMoverMemorizedElementEvent, dispatchMoverMoveFocusEvent, disposeTabster, forceCleanup, getCrossOrigin, getDeloser, getDummyInputContainer, getGroupper, getInternal, getModalizer, getMover, getObservedElement, getOutline, getRestorer, getShadowDOMAPI, getTabster, getTabsterAttribute, isNoOp, makeNoOp, mergeTabsterProps, setTabsterAttribute };
