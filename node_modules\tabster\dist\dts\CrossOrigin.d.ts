/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import { Subscribable } from "./State/Subscribable";
import * as Types from "./Types";
interface CrossOriginInstanceContext {
    ignoreKeyboardNavigationStateUpdate: boolean;
    focusOwner?: string;
    focusOwnerTimestamp?: number;
    deloserByUId: {
        [uid: string]: Types.Deloser;
    };
    origOutlineSetup?: (props?: Partial<Types.OutlineProps>) => void;
}
interface KnownTargets {
    [id: string]: {
        send: (payload: Types.CrossOriginTransactionData<any, any>) => void;
        last?: number;
    };
}
declare abstract class CrossOriginTransaction<I, O> {
    abstract type: Types.CrossOriginTransactionType;
    readonly id: string;
    readonly beginData: I;
    readonly timeout?: number;
    protected tabster: Types.TabsterCore;
    protected endData: O | undefined;
    protected owner: Types.GetWindow;
    protected ownerId: string;
    protected sendUp: Types.CrossOriginTransactionSend | undefined;
    private _promise;
    protected _resolve: ((endData?: O | PromiseLike<O>) => void) | undefined;
    private _reject;
    private _knownTargets;
    private _sentTo;
    protected targetId: string | undefined;
    private _inProgress;
    private _isDone;
    private _isSelfResponding;
    private _sentCount;
    constructor(tabster: Types.TabsterCore, getOwner: Types.GetWindow, knownTargets: KnownTargets, value: I, timeout?: number, sentTo?: Types.CrossOriginSentTo, targetId?: string, sendUp?: Types.CrossOriginTransactionSend);
    protected getTargets(knownTargets: KnownTargets): KnownTargets | null;
    begin(selfResponse?: (data: Types.CrossOriginTransactionData<I, O>) => Promise<O | undefined>): Promise<O | undefined>;
    private _send;
    end(error?: string): void;
    onResponse(data: Types.CrossOriginTransactionData<I, O>): void;
}
interface CrossOriginTransactionClass<I, O> {
    new (tabster: Types.TabsterCore, getOwner: Types.GetWindow, knownTargets: KnownTargets, value: I, timeout?: number, sentTo?: Types.CrossOriginSentTo, targetId?: string, sendUp?: Types.CrossOriginTransactionSend): CrossOriginTransaction<I, O>;
    shouldForward?(tabster: Types.TabsterCore, data: Types.CrossOriginTransactionData<I, O>, getOwner: Types.GetWindow, ownerId: string): boolean;
    makeResponse(tabster: Types.TabsterCore, data: Types.CrossOriginTransactionData<I, O>, getOwner: Types.GetWindow, ownerId: string, transactions: CrossOriginTransactions, forwardResult: Promise<O | undefined>, isSelfResponse?: boolean): Promise<O>;
    shouldSelfRespond?(tabster: Types.TabsterCore, data: I, getOwner: Types.GetWindow, ownerId: string): boolean;
}
declare class CrossOriginTransactions {
    private _owner;
    private _ownerUId;
    private _knownTargets;
    private _transactions;
    private _tabster;
    private _pingTimer;
    private _isDefaultSendUp;
    private _deadPromise;
    isSetUp: boolean;
    sendUp: Types.CrossOriginTransactionSend | undefined;
    ctx: CrossOriginInstanceContext;
    constructor(tabster: Types.TabsterCore, getOwner: Types.GetWindow, context: CrossOriginInstanceContext);
    setup(sendUp?: Types.CrossOriginTransactionSend | null): (msg: Types.CrossOriginMessage) => void;
    setSendUp(sendUp?: Types.CrossOriginTransactionSend | null): (msg: Types.CrossOriginMessage) => void;
    dispose(): Promise<void>;
    beginTransaction<I, O>(Transaction: CrossOriginTransactionClass<I, O>, value: I, timeout?: number, sentTo?: Types.CrossOriginSentTo, targetId?: string, withReject?: boolean): Promise<O | undefined>;
    removeTarget(uid: string): void;
    private _beginTransaction;
    forwardTransaction(data: Types.CrossOriginTransactionData<any, any>): Promise<any>;
    private _getTransactionClass;
    private _onMessage;
    private _onPageHide;
    private _dead;
    private _ping;
    private _onBrowserMessage;
}
export declare class CrossOriginElement implements Types.CrossOriginElement {
    private _tabster;
    readonly uid: string;
    readonly ownerId: string;
    readonly id?: string;
    readonly rootId?: string;
    readonly observedName?: string;
    readonly observedDetails?: string;
    constructor(tabster: Types.TabsterCore, uid: string, ownerId: string, id?: string, rootId?: string, observedName?: string, observedDetails?: string);
    focus(noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
}
export declare class CrossOriginFocusedElementState extends Subscribable<CrossOriginElement | undefined, Types.FocusedElementDetail> implements Types.CrossOriginFocusedElementState {
    private _transactions;
    constructor(transactions: CrossOriginTransactions);
    focus(element: Types.CrossOriginElement, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
    focusById(elementId: string, rootId?: string, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
    focusByObservedName(observedName: string, timeout?: number, rootId?: string, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean): Promise<boolean>;
    private _focus;
    static setVal(instance: Types.CrossOriginFocusedElementState, val: CrossOriginElement | undefined, detail: Types.FocusedElementDetail): void;
}
export declare class CrossOriginObservedElementState extends Subscribable<CrossOriginElement, Types.ObservedElementProps> implements Types.CrossOriginObservedElementState {
    private _tabster;
    private _transactions;
    private _lastRequestFocusId;
    constructor(tabster: Types.TabsterCore, transactions: CrossOriginTransactions);
    getElement(observedName: string, accessibility?: Types.ObservedElementAccessibility): Promise<CrossOriginElement | null>;
    waitElement(observedName: string, timeout: number, accessibility?: Types.ObservedElementAccessibility): Promise<CrossOriginElement | null>;
    requestFocus(observedName: string, timeout: number): Promise<boolean>;
    static trigger(instance: Types.CrossOriginObservedElementState, element: CrossOriginElement, details: Types.ObservedElementProps): void;
}
export declare class CrossOriginAPI implements Types.CrossOriginAPI {
    private _tabster;
    private _win;
    private _transactions;
    private _blurTimer;
    private _ctx;
    focusedElement: Types.CrossOriginFocusedElementState;
    observedElement: Types.CrossOriginObservedElementState;
    constructor(tabster: Types.TabsterCore);
    setup(sendUp?: Types.CrossOriginTransactionSend | null): (msg: Types.CrossOriginMessage) => void;
    isSetUp(): boolean;
    private _init;
    dispose(): void;
    private _onKeyboardNavigationStateChanged;
    private _onFocus;
    private _onObserved;
    private _outlineSetup;
}
export {};
