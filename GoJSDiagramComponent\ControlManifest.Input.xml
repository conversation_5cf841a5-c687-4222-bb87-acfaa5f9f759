<?xml version="1.0" encoding="utf-8" ?>
<manifest>
  <control namespace="DiagramNamespace" constructor="GoJSDiagramComponent" version="1.0.0" display-name-key="GoJS Diagram Component" description-key="Interactive diagram component using GoJS library" control-type="standard" >
    <external-service-usage enabled="false">
    </external-service-usage>
    
    <!-- Minimal property required by PCF framework -->
    <property name="dummyProperty" display-name-key="Dummy Property" description-key="Dummy property required by PCF framework" of-type="SingleLine.Text" usage="bound" required="false" />
    
    <resources>
      <code path="index.ts" order="1"/>
      <css path="styles/DiagramComponent.css" order="1" />
    </resources>
    
    <feature-usage>
      <uses-feature name="WebAPI" required="false" />
    </feature-usage>
  </control>
</manifest>
