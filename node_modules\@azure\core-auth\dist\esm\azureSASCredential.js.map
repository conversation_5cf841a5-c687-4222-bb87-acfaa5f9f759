{"version": 3, "file": "azureSASCredential.js", "sourceRoot": "", "sources": ["../../src/azureSASCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,sBAAsB,EAAE,MAAM,kBAAkB,CAAC;AAY1D;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAG7B;;OAEG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACH,YAAY,SAAiB;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,YAAoB;QAChC,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;IACjC,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,UAAmB;IACjD,OAAO,CACL,sBAAsB,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,QAAQ,CAC9F,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObjectWithProperties } from \"@azure/core-util\";\n\n/**\n * Represents a credential defined by a static shared access signature.\n */\nexport interface SASCredential {\n  /**\n   * The value of the shared access signature represented as a string\n   */\n  readonly signature: string;\n}\n\n/**\n * A static-signature-based credential that supports updating\n * the underlying signature value.\n */\nexport class AzureSASCredential implements SASCredential {\n  private _signature: string;\n\n  /**\n   * The value of the shared access signature to be used in authentication\n   */\n  public get signature(): string {\n    return this._signature;\n  }\n\n  /**\n   * Create an instance of an AzureSASCredential for use\n   * with a service client.\n   *\n   * @param signature - The initial value of the shared access signature to use in authentication\n   */\n  constructor(signature: string) {\n    if (!signature) {\n      throw new Error(\"shared access signature must be a non-empty string\");\n    }\n\n    this._signature = signature;\n  }\n\n  /**\n   * Change the value of the signature.\n   *\n   * Updates will take effect upon the next request after\n   * updating the signature value.\n   *\n   * @param newSignature - The new shared access signature value to be used\n   */\n  public update(newSignature: string): void {\n    if (!newSignature) {\n      throw new Error(\"shared access signature must be a non-empty string\");\n    }\n\n    this._signature = newSignature;\n  }\n}\n\n/**\n * Tests an object to determine whether it implements SASCredential.\n *\n * @param credential - The assumed SASCredential to be tested.\n */\nexport function isSASCredential(credential: unknown): credential is SASCredential {\n  return (\n    isObjectWithProperties(credential, [\"signature\"]) && typeof credential.signature === \"string\"\n  );\n}\n"]}