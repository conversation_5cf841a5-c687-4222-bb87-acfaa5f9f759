/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import type { Restorer as RestorerInterface, RestorerAPI as RestorerAPIType, RestorerProps, TabsterCore } from "./Types";
import { TabsterPart } from "./Utils";
declare class Restorer extends TabsterPart<RestorerProps> implements RestorerInterface {
    private _hasFocus;
    constructor(tabster: TabsterCore, element: HTMLElement, props: RestorerProps);
    dispose(): void;
    private _onFocusOut;
    private _onFocusIn;
}
export declare class RestorerAPI implements RestorerAPIType {
    private _tabster;
    private _history;
    private _keyboardNavState;
    private _focusedElementState;
    private _getWindow;
    constructor(tabster: TabsterCore);
    dispose(): void;
    private _onRestoreFocus;
    private _onFocusIn;
    private _restoreFocus;
    createRestorer(element: HTMLElement, props: RestorerProps): Restorer;
}
export {};
