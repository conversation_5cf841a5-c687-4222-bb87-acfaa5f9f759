{"name": "tabster", "version": "8.5.6", "description": "Focus Management Tools for Web", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/tabster.esm.js", "typings": "./dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/microsoft/tabster"}, "scripts": {"build": "npm run clean && npm run build-bundle && npm run build-storybook", "build-bundle": "rollup -c", "build-docs": "cd docs && npm install && npm run build", "build-storybook": "storybook build", "bundle-size": "npm run build-bundle && bundle-size measure", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint src/ --fix", "lint:check": "eslint src/", "test": "node tests/utils/runner.js", "test:uncontrolled": "STORYBOOK_UNCONTROLLED=true npm test", "test:root-dummy-inputs": "STORYBOOK_UNCONTROLLED=true STORYBOOK_ROOT_DUMMY_INPUTS=true npm test", "test:shadowdom": "SHADOWDOM=true npm test", "test:shadowdom:uncontrolled": "SHADOWDOM=true STORYBOOK_UNCONTROLLED=true npm test", "test:shadowdom:root-dummy-inputs": "SHADOWDOM=true STORYBOOK_UNCONTROLLED=true STORYBOOK_ROOT_DUMMY_INPUTS=true npm test", "test:all": "npm run test && npm run test:uncontrolled && npm run test:root-dummy-inputs && npm run test:shadowdom && npm run test:shadowdom:uncontrolled && npm run test:shadowdom:root-dummy-inputs", "prepublishOnly": "npm run build", "release": "release-it", "serve": "npx http-serve storybook-static", "serve-docs": "cd docs && npm run serve", "start": "npm run storybook", "start-docs": "cd docs && npm run start", "start:uncontrolled": "STORYBOOK_UNCONTROLLED=true npm run storybook", "start:root-dummy-inputs": "STORYBOOK_UNCONTROLLED=true STORYBOOK_ROOT_DUMMY_INPUTS=true npm run storybook", "storybook": "storybook dev -p 8080", "type-check": "npm run type-check:lib && npm run type-check:tests && npm run type-check:stories", "type-check:lib": "tsc -b  src/tsconfig.lib.json", "type-check:tests": "tsc -b tests/tsconfig.spec.json", "type-check:stories": "tsc -b stories/tsconfig.stories.json", "prepare-pages-deploy": "rimraf ./.pages-deploy && npm run build-docs && npm run build-storybook && copyfiles -u 2 -a \"./docs/build/**/*\" ./.pages-deploy && copyfiles -u 1 -a \"./storybook-static/**/*\" ./.pages-deploy/storybook"}, "dependencies": {"keyborg": "2.6.0", "tslib": "^2.8.1"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.27.0", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@storybook/addon-actions": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-webpack5-compiler-babel": "^3.0.6", "@storybook/builder-webpack5": "^8.6.12", "@storybook/html": "^8.6.12", "@storybook/html-webpack5": "^8.6.12", "@storybook/preset-html-webpack": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "@types/expect-puppeteer": "^5.0.6", "@types/jest": "^29.5.14", "@types/jest-environment-puppeteer": "^5.0.6", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "auto-changelog": "^2.5.0", "babel-jest": "^29.7.0", "babel-loader": "^10.0.0", "babel-plugin-annotate-pure-calls": "^0.5.0", "copyfiles": "^2.4.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.31.0", "jest": "^29.7.0", "jest-puppeteer": "^11.0.0", "monosize": "^0.6.3", "monosize-bundler-webpack": "0.1.6", "monosize-storage-azure": "0.0.16", "prettier": "^3.5.3", "puppeteer": "^24.7.1", "react": "^18.3.1", "react-dom": "^18.3.1", "release-it": "^19.0.1", "rimraf": "^6.0.1", "rollup": "^4.40.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-typescript2": "^0.36.0", "strip-ansi": "^7.1.0", "tree-kill": "^1.2.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.40.0"}}