{"version": 3, "sources": ["../src/components/Accordion/Accordion.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { renderAccordion_unstable } from './renderAccordion';\nimport { useAccordion_unstable } from './useAccordion';\nimport { useAccordionContextValues_unstable } from './useAccordionContextValues';\nimport { useCustomStyleHook_unstable } from '@fluentui/react-shared-contexts';\nimport { useAccordionStyles_unstable } from './useAccordionStyles.styles';\nimport type { AccordionProps } from './Accordion.types';\nimport type { ForwardRefComponent, JSXElement } from '@fluentui/react-utilities';\n\n/**\n * Define a styled Accordion, using the `useAccordion_unstable` and `useAccordionStyles_unstable` hooks.\n */\nexport const Accordion: ForwardRefComponent<AccordionProps> & (<TItem>(props: AccordionProps<TItem>) => JSXElement) =\n  React.forwardRef<HTMLDivElement, AccordionProps>((props, ref) => {\n    const state = useAccordion_unstable(props, ref);\n    const contextValues = useAccordionContextValues_unstable(state);\n\n    useAccordionStyles_unstable(state);\n\n    useCustomStyleHook_unstable('useAccordionStyles_unstable')(state);\n\n    return renderAccordion_unstable(state, contextValues);\n  }) as ForwardRefComponent<AccordionProps> & (<TItem>(props: AccordionProps<TItem>) => JSXElement);\n\nAccordion.displayName = 'Accordion';\n"], "names": ["React", "renderAccordion_unstable", "useAccordion_unstable", "useAccordionContextValues_unstable", "useCustomStyleHook_unstable", "useAccordionStyles_unstable", "Accordion", "forwardRef", "props", "ref", "state", "contextValues", "displayName"], "mappings": "AAAA,YAAYA,WAAW,QAAQ;AAC/B,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,kCAAkC,QAAQ,8BAA8B;AACjF,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,2BAA2B,QAAQ,8BAA8B;AAI1E;;CAEC,GACD,OAAO,MAAMC,0BACXN,MAAMO,UAAU,CAAiC,CAACC,OAAOC;IACvD,MAAMC,QAAQR,sBAAsBM,OAAOC;IAC3C,MAAME,gBAAgBR,mCAAmCO;IAEzDL,4BAA4BK;IAE5BN,4BAA4B,+BAA+BM;IAE3D,OAAOT,yBAAyBS,OAAOC;AACzC,GAAkG;AAEpGL,UAAUM,WAAW,GAAG"}