{"version": 3, "file": "logPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/logPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AA2ClC,8BAuBC;AA7DD,sCAAiD;AACjD,uDAAiD;AAEjD;;GAEG;AACU,QAAA,aAAa,GAAG,WAAW,CAAC;AA4BzC;;;GAGG;AACH,SAAgB,SAAS,CAAC,UAA4B,EAAE;;IACtD,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,eAAU,CAAC,IAAI,CAAC;IACjD,MAAM,SAAS,GAAG,IAAI,wBAAS,CAAC;QAC9B,4BAA4B,EAAE,OAAO,CAAC,4BAA4B;QAClE,gCAAgC,EAAE,OAAO,CAAC,gCAAgC;KAC3E,CAAC,CAAC;IACH,OAAO;QACL,IAAI,EAAE,qBAAa;QACnB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,CAAC,YAAY,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YAErC,MAAM,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,YAAY,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE3D,OAAO,QAAQ,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport type { Debugger } from \"@azure/logger\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { logger as coreLogger } from \"../log.js\";\nimport { Sanitizer } from \"../util/sanitizer.js\";\n\n/**\n * The programmatic identifier of the logPolicy.\n */\nexport const logPolicyName = \"logPolicy\";\n\n/**\n * Options to configure the logPolicy.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n\n  /**\n   * The log function to use for writing pipeline logs.\n   * Defaults to core-http's built-in logger.\n   * Compatible with the `debug` library.\n   */\n  logger?: Debugger;\n}\n\n/**\n * A policy that logs all requests and responses.\n * @param options - Options to configure logPolicy.\n */\nexport function logPolicy(options: LogPolicyOptions = {}): PipelinePolicy {\n  const logger = options.logger ?? coreLogger.info;\n  const sanitizer = new Sanitizer({\n    additionalAllowedHeaderNames: options.additionalAllowedHeaderNames,\n    additionalAllowedQueryParameters: options.additionalAllowedQueryParameters,\n  });\n  return {\n    name: logPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!logger.enabled) {\n        return next(request);\n      }\n\n      logger(`Request: ${sanitizer.sanitize(request)}`);\n\n      const response = await next(request);\n\n      logger(`Response status code: ${response.status}`);\n      logger(`Headers: ${sanitizer.sanitize(response.headers)}`);\n\n      return response;\n    },\n  };\n}\n"]}