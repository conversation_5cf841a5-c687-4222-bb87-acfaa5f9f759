/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
import { DummyInputManager, TabsterPart, WeakHTMLElement } from "./Utils";
declare class GroupperDummyManager extends DummyInputManager {
    constructor(element: WeakHTMLElement, groupper: Groupper, tabster: Types.TabsterCore, sys: Types.SysProps | undefined);
}
export declare class Groupper extends TabsterPart<Types.GroupperProps> implements Types.Groupper {
    private _shouldTabInside;
    private _first;
    private _onDispose;
    dummyManager: GroupperDummyManager | undefined;
    constructor(tabster: Types.TabsterCore, element: HTMLElement, onDispose: (groupper: Groupper) => void, props: Types.GroupperProps, sys: Types.SysProps | undefined);
    dispose(): void;
    findNextTabbable(currentElement?: HTMLElement, referenceElement?: HTMLElement, isBackward?: boolean, ignoreAccessibility?: boolean): Types.NextTabbable | null;
    makeTabbable(isTabbable: boolean): void;
    isActive(noIfFirstIsFocused?: boolean): boolean | undefined;
    getFirst(orContainer: boolean): HTMLElement | undefined;
    setFirst(element: HTMLElement | undefined): void;
    acceptElement(element: HTMLElement, state: Types.FocusableAcceptElementState): number | undefined;
}
export declare class GroupperAPI implements Types.GroupperAPI {
    private _tabster;
    private _updateTimer;
    private _win;
    private _current;
    private _grouppers;
    constructor(tabster: Types.TabsterCore, getWindow: Types.GetWindow);
    private _init;
    dispose(): void;
    createGroupper(element: HTMLElement, props: Types.GroupperProps, sys: Types.SysProps | undefined): Groupper;
    forgetCurrentGrouppers(): void;
    private _onGroupperDispose;
    private _onFocus;
    private _onMouseDown;
    private _updateCurrent;
    private _onKeyDown;
    private _onMoveFocus;
    private _enterGroupper;
    private _escapeGroupper;
    moveFocus(element: HTMLElement, action: Types.GroupperMoveFocusAction): HTMLElement | null;
    handleKeyPress(element: HTMLElement, event: KeyboardEvent, fromModalizer?: boolean): void;
}
export {};
