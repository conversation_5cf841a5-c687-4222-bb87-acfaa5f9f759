/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
export declare class ShadowMutationObserver implements MutationObserver {
    private static _shadowObservers;
    private _root?;
    private _options?;
    private _callback;
    private _observer;
    private _subObservers;
    private _isObserving;
    private static _overrideAttachShadow;
    constructor(callback: MutationCallback);
    private _callbackWrapper;
    private _addSubObserver;
    disconnect(): void;
    observe(target: Node, options?: MutationObserverInit): void;
    private _walkShadows;
    takeRecords(): MutationRecord[];
}
export declare function createShadowMutationObserver(callback: MutationCallback): MutationObserver;
