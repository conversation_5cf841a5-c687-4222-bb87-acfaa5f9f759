/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "../Types";
import { Subscribable } from "./Subscribable";
export declare class FocusedElementState extends Subscribable<HTMLElement | undefined, Types.FocusedElementDetail> implements Types.FocusedElementState {
    private static _lastResetElement;
    private static _isTabbingTimer;
    static isTabbing: boolean;
    private _tabster;
    private _win;
    private _nextVal;
    private _lastVal;
    private _asyncFocus?;
    constructor(tabster: Types.TabsterCore, getWindow: Types.GetWindow);
    private _init;
    dispose(): void;
    static forgetMemorized(instance: Types.FocusedElementState, parent: HTMLElement): void;
    getFocusedElement(): HTMLElement | undefined;
    getLastFocusedElement(): HTMLElement | undefined;
    focus(element: HTMLElement, noFocusedProgrammaticallyFlag?: boolean, noAccessibleCheck?: boolean, preventScroll?: boolean): boolean;
    focusDefault(container: HTMLElement): boolean;
    getFirstOrLastTabbable(isFirst: boolean, props: Pick<Types.FindFocusableProps, "container" | "ignoreAccessibility">): HTMLElement | undefined;
    private _focusFirstOrLast;
    focusFirst(props: Types.FindFirstProps): boolean;
    focusLast(props: Types.FindFirstProps): boolean;
    resetFocus(container: HTMLElement): boolean;
    requestAsyncFocus(source: Types.AsyncFocusSource, callback: () => void, delay: number): void;
    cancelAsyncFocus(source: Types.AsyncFocusSource): void;
    private _setOrRemoveAttribute;
    private _setFocusedElement;
    protected setVal(val: HTMLElement | undefined, detail: Types.FocusedElementDetail): void;
    private _onFocusIn;
    private _onFocusOut;
    static findNextTabbable(tabster: Types.TabsterCore, ctx: Types.TabsterContext, container?: HTMLElement, currentElement?: HTMLElement, referenceElement?: HTMLElement, isBackward?: boolean, ignoreAccessibility?: boolean): Types.NextTabbable | null;
    private _validateFocusedElement;
    private _onKeyDown;
    _onChanged: (element: HTMLElement | undefined, detail: Types.FocusedElementDetail) => void;
}
