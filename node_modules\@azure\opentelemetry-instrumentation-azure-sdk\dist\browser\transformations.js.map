{"version": 3, "file": "transformations.js", "sourceRoot": "", "sources": ["../../src/transformations.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,UAAU,uBAAuB,CACrC,eAAmB;IAEnB,MAAM,GAAG,GAAG,CAAC,eAAe,IAAI,UAAU,CAAC,CAAC,WAAW,EAA2B,CAAC;IACnF,OAAO,QAAQ,CAAC,GAAG,CAAuB,CAAC;AAC7C,CAAC;AAaD;;;;;GAKG;AACH,SAAS,oBAAoB,CAAC,YAA+B,EAAE;IAC7D,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;QAC/C,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACzE,IAAI,WAAW,EAAE,CAAC;YAChB,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,WAAW;gBACpB,UAAU,EAAE,kBAAkB,CAAC,eAAe,CAAC,UAAU,CAAC;aAC3D,CAAC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAY,CAAC,CAAC;AACnB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,WAAqC;IACjE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,WAAW,IAAI,EAAE,CAAC;IAElE,MAAM,UAAU,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;IAE9C,OAAO;QACL,UAAU;QACV,IAAI;QACJ,KAAK;KACN,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  InstrumenterSpanOptions,\n  TracingSpanKind,\n  TracingSpanLink,\n} from \"@azure/core-tracing\";\nimport type { Link, SpanOptions } from \"@opentelemetry/api\";\nimport { SpanKind, trace } from \"@opentelemetry/api\";\nimport { sanitizeAttributes } from \"@opentelemetry/core\";\n\n/**\n * Converts our TracingSpanKind to the corresponding OpenTelemetry SpanKind.\n *\n * By default it will return {@link SpanKind.INTERNAL}\n * @param tracingSpanKind - The core tracing {@link TracingSpanKind}\n * @returns - The OpenTelemetry {@link SpanKind}\n */\nexport function toOpenTelemetrySpanKind<K extends TracingSpanKind>(\n  tracingSpanKind?: K,\n): SpanKindMapping[K] {\n  const key = (tracingSpanKind || \"internal\").toUpperCase() as keyof typeof SpanKind;\n  return SpanKind[key] as SpanKindMapping[K];\n}\n\n/**\n * A mapping between our {@link TracingSpanKind} union type and OpenTelemetry's {@link SpanKind}.\n */\ntype SpanKindMapping = {\n  client: SpanKind.CLIENT;\n  server: SpanKind.SERVER;\n  producer: SpanKind.PRODUCER;\n  consumer: SpanKind.CONSUMER;\n  internal: SpanKind.INTERNAL;\n};\n\n/**\n * Converts core-tracing's TracingSpanLink to OpenTelemetry's Link\n *\n * @param spanLinks - The core tracing {@link TracingSpanLink} to convert\n * @returns A set of {@link Link}s\n */\nfunction toOpenTelemetryLinks(spanLinks: TracingSpanLink[] = []): Link[] {\n  return spanLinks.reduce((acc, tracingSpanLink) => {\n    const spanContext = trace.getSpanContext(tracingSpanLink.tracingContext);\n    if (spanContext) {\n      acc.push({\n        context: spanContext,\n        attributes: sanitizeAttributes(tracingSpanLink.attributes),\n      });\n    }\n    return acc;\n  }, [] as Link[]);\n}\n\n/**\n * Converts core-tracing span options to OpenTelemetry options.\n *\n * @param spanOptions - The {@link InstrumenterSpanOptions} to convert.\n * @returns An OpenTelemetry {@link SpanOptions} that can be used when creating a span.\n */\nexport function toSpanOptions(spanOptions?: InstrumenterSpanOptions): SpanOptions {\n  const { spanAttributes, spanLinks, spanKind } = spanOptions || {};\n\n  const attributes = sanitizeAttributes(spanAttributes);\n  const kind = toOpenTelemetrySpanKind(spanKind);\n  const links = toOpenTelemetryLinks(spanLinks);\n\n  return {\n    attributes,\n    kind,\n    links,\n  };\n}\n"]}